import 'package:leadrat/features/attendance/data/models/attendance_logs_list_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_settings_model.dart';
import '../../models/attendance_post_model.dart';

abstract class AttendanceRemoteDataSource {
  Future<List<AttendanceLogsListModel>?> getAttendanceHistory({
    int pageNumber = 1,
    int pageSize = 10,
    String? fromDate,
    String? toDate,
  });

  Future<List<AttendanceModel>?> getAttendanceLogByUser(String userid, String timeZone);

  Future<AttendanceSettingsModel?> getAttendanceSettings();

  Future<String?> postClockInAttendance(AttendancePostModel clockInModel);

  Future<bool?> postClockOutAttendance(AttendancePostModel clockOutModel);

  Future<List<AttendanceModel>?> getTodaysClockInsByWithTimeZone(String userId, String timeZoneId, String baseUtcOffset, String startTime);
}
