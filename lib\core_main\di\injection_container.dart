import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:leadrat/core_main/common/bloc/brevo_email_template_bloc/brevo_email_template_bloc.dart';
import 'package:leadrat/core_main/common/bloc/message_templates_bloc/message_template_bloc.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_bloc.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_cubit.dart';
import 'package:leadrat/core_main/common/bloc/search_bloc/search_bloc.dart';
import 'package:leadrat/core_main/common/cubit/basic_user_details_cubit.dart';
import 'package:leadrat/core_main/common/cubit/image_preview_cubit.dart';
import 'package:leadrat/core_main/common/cubit/readmore_cubit.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/local/app_analysis_local_data_data_source.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/local/app_analysis_local_data_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/remote/app_analysis_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/app_analysis/data_source/remote/app_analysis_remote_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository_impl.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/local/auth_token_local_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/local/auth_token_local_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/remote/auth_token_data_source.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/data_source/remote/auth_token_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository.dart';
import 'package:leadrat/core_main/common/data/auth_tokens/repository/auth_token_repository_impl.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/remote/custom_amenitiesAndAttributes_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/repository/custom_amenities_and_attributes_repository.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/repository/custom_amenities_and_attributes_repository_impl.dart';
import 'package:leadrat/core_main/common/data/custom_email/data_source/remote/custom_email_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_email/data_source/remote/custom_email_remote_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/custom_email/repository/custom_email_repository.dart';
import 'package:leadrat/core_main/common/data/custom_email/repository/custom_email_repository_impl.dart';
import 'package:leadrat/core_main/common/data/device/data_source/device_data_source.dart';
import 'package:leadrat/core_main/common/data/device/data_source/device_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/device/repository/device_repository.dart';
import 'package:leadrat/core_main/common/data/device/repository/device_repository_impl.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/local/flag_local_data_source.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/local/flag_local_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/remote/flag_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/flags/data_source/remote/flag_remote_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/flags/repository/flag_repository.dart';
import 'package:leadrat/core_main/common/data/flags/repository/flag_repository_impl.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/local/gloabal_settings_local_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/local/global_settings_local_data_source.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/remote/global_setting_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/remote/global_setting_remote_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository_impl.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/remote/masterdata_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/remote/masterdata_remote_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository_impl.dart';
import 'package:leadrat/core_main/common/data/saved_filter/data_source/remote/saved_filter_remote_data_source.dart';
import 'package:leadrat/core_main/common/data/saved_filter/data_source/remote/saved_filter_remote_data_source_impl.dart';
import 'package:leadrat/core_main/common/data/saved_filter/repository/saved_filter_repository.dart';
import 'package:leadrat/core_main/common/data/saved_filter/repository/saved_filter_repository_impl.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/managers/app_version_manager/app_version_manager.dart';
import 'package:leadrat/core_main/managers/app_version_manager/app_version_manager_impl.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/shared_preference_manager.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/token_manager.dart';
import 'package:leadrat/core_main/mapper/lead_mapper.dart';
import 'package:leadrat/core_main/mapper/project_mapper.dart';
import 'package:leadrat/core_main/mapper/property_mapper.dart';
import 'package:leadrat/core_main/mapper/prospect_mapper.dart';
import 'package:leadrat/core_main/services/blob_storage_service/blob_storage_service.dart';
import 'package:leadrat/core_main/services/blob_storage_service/blob_storage_service_impl.dart';
import 'package:leadrat/core_main/services/ivr_service/ivr_service.dart';
import 'package:leadrat/core_main/services/ivr_service/ivr_service_impl.dart';
import 'package:leadrat/core_main/services/password_service/password_service.dart';
import 'package:leadrat/core_main/services/password_service/password_service_impl.dart';
import 'package:leadrat/core_main/services/secret_manager_service/secret_manager_local_service.dart';
import 'package:leadrat/core_main/services/utility_service/utility_service.dart';
import 'package:leadrat/core_main/services/utility_service/utility_service_impl.dart';
import 'package:leadrat/core_main/services/whatsapp_web_socket_service/whatsapp_web_socket_service.dart';
import 'package:leadrat/core_main/services/whatsapp_web_socket_service/whatsapp_web_socket_service_impl.dart';
import 'package:leadrat/features/attendance/data/data_source/remote/attendance_remote_data_source.dart';
import 'package:leadrat/features/attendance/data/data_source/remote/attendance_remote_data_source_impl.dart';
import 'package:leadrat/features/attendance/data/repository/attendance_repository_impl.dart';
import 'package:leadrat/features/attendance/domain/repository/attendance_repository.dart';
import 'package:leadrat/features/attendance/domain/usecase/attendance_history_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/get_attendance_logs_by_user_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/get_attendance_settings_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/post_clockin_attendance_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/post_clockout_attendance_usecase.dart';
import 'package:leadrat/features/attendance/presentation/bloc/attendance_bloc/attendance_bloc.dart';
import 'package:leadrat/features/auth/data/data_source/local/auth_local_data_source.dart';
import 'package:leadrat/features/auth/data/data_source/local/auth_local_data_source_impl.dart';
import 'package:leadrat/features/auth/data/data_source/remote/auth_remote_data_source.dart';
import 'package:leadrat/features/auth/data/data_source/remote/auth_remote_data_source_imp.dart';
import 'package:leadrat/features/auth/data/repository/auth_repository_impl.dart';
import 'package:leadrat/features/auth/domain/repository/auth_repository.dart';
import 'package:leadrat/features/auth/domain/usecase/generate_multi_factor_auth_otp_use_case.dart';
import 'package:leadrat/features/auth/domain/usecase/generated_otp_usecase.dart';
import 'package:leadrat/features/auth/domain/usecase/get_tenant_details_usecase.dart';
import 'package:leadrat/features/auth/domain/usecase/get_username_details_usecase.dart';
import 'package:leadrat/features/auth/domain/usecase/login_use_case.dart';
import 'package:leadrat/features/auth/domain/usecase/otp_verification_usecase.dart';
import 'package:leadrat/features/auth/domain/usecase/post_device_info_and_pns_registration_usecase.dart';
import 'package:leadrat/features/auth/domain/usecase/reset_password_with_otp_usecase.dart';
import 'package:leadrat/features/auth/domain/usecase/verify_multi_factor_auth_otp_use_case.dart';
import 'package:leadrat/features/auth/presentation/bloc/auth_domain_bloc/auth_domain_bloc.dart';
import 'package:leadrat/features/auth/presentation/bloc/auth_forget_password_bloc/auth_forget_password_bloc.dart';
import 'package:leadrat/features/auth/presentation/bloc/connect_domain_bloc/connect_domain_bloc.dart';
import 'package:leadrat/features/auth/presentation/bloc/multifactor_authentication_bloc/multi_factor_authentication_bloc.dart';
import 'package:leadrat/features/auth/presentation/bloc/recover_account_bloc/recover_account_bloc.dart';
import 'package:leadrat/features/auth/presentation/bloc/reset_password/reset_password_bloc.dart';
import 'package:leadrat/features/auth/presentation/bloc/verify_otp_bloc/verify_otp_bloc.dart';
import 'package:leadrat/features/dashboard/data/data_source/remote/dashboard_remote_data_source.dart';
import 'package:leadrat/features/dashboard/data/data_source/remote/dashboard_remote_data_source_impl.dart';
import 'package:leadrat/features/dashboard/data/repository/dashboard_repository_impl.dart';
import 'package:leadrat/features/dashboard/domain/repository/dashboard_repository.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_call_status_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_all_lead_custom_status_count_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_custom_lead_source_by_user_countusecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_custom_lead_source_by_user_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_data_by_user_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_lead_source_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_user_calls_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_dashboard_user_whats_app_message_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_lead_pipeline_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_lead_received_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_lead_resource_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_lead_tracker_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_meeting_status_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_site_visit_status_usecase.dart';
import 'package:leadrat/features/dashboard/domain/usecase/get_user_status_count_usecase.dart';
import 'package:leadrat/features/dashboard/presentation/bloc/custom_dashboard_bloc/custom_dashboard_bloc.dart';
import 'package:leadrat/features/dashboard/presentation/bloc/dashboard_bloc/dashboard_bloc.dart';
import 'package:leadrat/features/dashboard/presentation/bloc/dashboard_filter_bloc/dashboard_filter_bloc.dart';
import 'package:leadrat/features/data_management/data/data_source/local/data_management_local_data_source.dart';
import 'package:leadrat/features/data_management/data/data_source/local/data_management_local_data_source_impl.dart';
import 'package:leadrat/features/data_management/data/data_source/remote/data_management_remote_data_source.dart';
import 'package:leadrat/features/data_management/data/data_source/remote/data_management_remote_data_source_impl.dart';
import 'package:leadrat/features/data_management/data/repository/prospect_repository_impl.dart';
import 'package:leadrat/features/data_management/domain/repository/prospect_repository.dart';
import 'package:leadrat/features/data_management/domain/usecase/add_data_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/check_prospect_assigned_by_prospect_id.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_call_through_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_data_by_contact_no_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_data_communication_use_case.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_data_history_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_data_search_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_count_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_details_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_excel_data_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_location_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_notes_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_source_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_status_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_sub_source_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_template_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/prospect_reassign_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_convert_to_lead_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_data_status_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_data_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_prospect_action_count_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_prospect_call_log_use_case.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_prospect_notes_usecase.dart';
import 'package:leadrat/features/data_management/presentation/bloc/add_data_bloc/add_data_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/convert_to_lead_bloc/convert_to_lead_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/custom_add_data_bloc/custom_add_data_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_details_bloc/data_details_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_filter_bloc/data_filter_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_history_bloc/data_history_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_notes_bloc/data_notes_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_search_bloc/data_search_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/manage_data_bloc/manage_data_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/update_data_status_bloc/update_data_status_bloc.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/home/<USER>/bloc/splash_cubit/splash_cubit.dart';
import 'package:leadrat/features/lead/data/data_source/local/leads_card_view_local_data_source.dart';
import 'package:leadrat/features/lead/data/data_source/local/leads_card_view_local_data_source_impl.dart';
import 'package:leadrat/features/lead/data/data_source/remote/leads_card_view_remote_data_source.dart';
import 'package:leadrat/features/lead/data/data_source/remote/leads_card_view_remote_data_source_impl.dart';
import 'package:leadrat/features/lead/data/data_source/remote/leads_remote_data_source.dart';
import 'package:leadrat/features/lead/data/data_source/remote/leads_remote_data_source_impl.dart';
import 'package:leadrat/features/lead/data/repository/lead_repository_impl.dart';
import 'package:leadrat/features/lead/data/repository/leads_card_view_repository_impl.dart';
import 'package:leadrat/features/lead/domain/repository/leads_card_view_repository.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';
import 'package:leadrat/features/lead/domain/usecase/add_lead_documents_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/add_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/add_projects_in_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/check_lead_assigned_by_leadId_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/delete_lead_document_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_Lead_excel_Data_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_additional_property_keys_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_additional_property_values_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_all_custom_statuses_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_all_initial_custom_leads_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_all_initial_leads_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_all_statuses_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_apppointments_by_projects_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/get_channel_partner_names_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_by_contact_no_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_history_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_info_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_notes_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_property_module_wise_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_sub_source_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_leads_communication_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_matching_projects_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/get_matching_properties_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_property_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/re_assign_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/search_leads_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_appointments_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_clicked_link_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/update_custom_filter_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_call_log_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_contact_count_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_flag_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_note_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_status_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_leads_categories_orders_use_case.dart';
import 'package:leadrat/features/lead/presentation/bloc/add_lead_bloc/add_lead_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/appointments_bloc/appointments_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/custom_add_lead_bloc/custom_add_lead_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_documents_bloc/lead_documents_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_filter_bloc/lead_filter_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_history_bloc/lead_history_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_notes_bloc/lead_notes_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/manage_leads_bloc/manage_leads_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/matching_projects_bloc/matching_projects_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/matching_properties_bloc/matching_properties_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/update_lead_status_bloc/update_lead_status_bloc.dart';
import 'package:leadrat/features/listing_management/data/data_source/listing_management_remote_data_source.dart';
import 'package:leadrat/features/listing_management/data/data_source/listing_management_remote_data_source_impl.dart';
import 'package:leadrat/features/listing_management/data/repository/listing_management_repository_impl.dart';
import 'package:leadrat/features/listing_management/domain/repository/listing_management_repository.dart';
import 'package:leadrat/features/listing_management/domain/usecase/add_property_listing_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/clone_property_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/get_all_listing_management_properties_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/get_listing_management_property_count_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/get_property_listing_details_by_id.dart';
import 'package:leadrat/features/listing_management/domain/usecase/search_property_listing_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/update_property_listing_use_case.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_bloc/listing_management_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_filter_bloc/listing_management_filter_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_facilities_bloc/property_listing_facilities_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_gallery_bloc/property_listing_gallery_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_location_bloc/property_listing_location_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_tab_bloc/property_listing_tab_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/propety_listing_property_info_bloc/property_listing_property_info_bloc.dart';
import 'package:leadrat/features/notification/data/data_source/remote/notification_remote_data_source.dart';
import 'package:leadrat/features/notification/data/data_source/remote/notification_remote_data_source_impl.dart';
import 'package:leadrat/features/notification/data/repository/notification_repository_impl.dart';
import 'package:leadrat/features/notification/domain/repository/notification_repository.dart';
import 'package:leadrat/features/notification/domain/usecase/get_notification_usecase.dart';
import 'package:leadrat/features/notification/domain/usecase/update_notification_status_usecase.dart';
import 'package:leadrat/features/notification/presentation/bloc/notification_bloc/notication_bloc.dart';
import 'package:leadrat/features/projects/data/data_source/local/project_local_data_source.dart';
import 'package:leadrat/features/projects/data/data_source/local/project_local_data_source_impl.dart';
import 'package:leadrat/features/projects/data/data_source/remote/project_remote_data_source.dart';
import 'package:leadrat/features/projects/data/data_source/remote/project_remote_data_source_impl.dart';
import 'package:leadrat/features/projects/data/repository/project_repository_impl.dart';
import 'package:leadrat/features/projects/domain/repository/project_repository.dart';
import 'package:leadrat/features/projects/domain/usecase/clone_project_usecase.dart';
import 'package:leadrat/features/projects/domain/usecase/delete_project_by_id_usecase.dart';
import 'package:leadrat/features/projects/domain/usecase/get_all_projects_usecase.dart';
import 'package:leadrat/features/projects/domain/usecase/get_builders_details_usecase.dart';
import 'package:leadrat/features/projects/domain/usecase/get_matching_leads_by_project_usecase.dart';
import 'package:leadrat/features/projects/domain/usecase/get_project_by_id_use_case.dart';
import 'package:leadrat/features/projects/domain/usecase/update_project_share_count_usecase.dart';
import 'package:leadrat/features/projects/presentation/blocs/manage_projects/manage_projects_bloc.dart';
import 'package:leadrat/features/projects/presentation/blocs/matching_leads_by_project_bloc/matching_leads_by_project_bloc.dart';
import 'package:leadrat/features/projects/presentation/blocs/project_filter_bloc/project_filter_bloc.dart';
import 'package:leadrat/features/projects/presentation/blocs/project_info_bloc/project_info_bloc.dart';
import 'package:leadrat/features/properties/data/data_source/listing_site_remote_data_source.dart';
import 'package:leadrat/features/properties/data/data_source/listing_site_remote_data_source_impl.dart';
import 'package:leadrat/features/properties/data/data_source/local/properties_local_data_source.dart';
import 'package:leadrat/features/properties/data/data_source/local/properties_local_data_source_impl.dart';
import 'package:leadrat/features/properties/data/repository/listing_site_repository_impl.dart';
import 'package:leadrat/features/properties/domain/repository/listing_site_repository.dart';
import 'package:leadrat/features/properties/domain/usecase/add_property_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/archive_property_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/delete_property_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/get_all_custom_listing_source_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/get_all_listing_site_addresses_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_all_property_listing_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/get_community_and_sub_community_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_image_diversity_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_matching_associate_leads_count_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_matching_leads_count_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_owner_names_or_addresses_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_project_amenities_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_property_by_title_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_property_listing_count_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/get_property_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/property_list_delist_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/re_assign_property_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/search_properties_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/update_property_assignedTo_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/update_property_share_count_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/update_property_use_case.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/basic_info_bloc/basic_info_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/facilities_tab_bloc/facilities_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/gallery_tab_bloc/gallery_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/property_info_tab_bloc/property_info_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/publishing_tab_bloc/publishing_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/matching_leads_bloc/matching_leads_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/property_filter_bloc/property_filter_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/property_info_bloc/property_info_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/property_share_bloc/property_share_bloc.dart';
import 'package:leadrat/features/qrcode/data/data_source/remote/qr_code_remote_data_source.dart';
import 'package:leadrat/features/qrcode/data/data_source/remote/qr_code_remote_data_source_impl.dart';
import 'package:leadrat/features/qrcode/data/repository/qr_code_repository_impl.dart';
import 'package:leadrat/features/qrcode/domain/repository/qr_code_repository.dart';
import 'package:leadrat/features/qrcode/domain/usecase/generate_qr_code_usecase.dart';
import 'package:leadrat/features/qrcode/domain/usecase/get_qr_code_templete_usecase.dart';
import 'package:leadrat/features/qrcode/presentation/bloc/qr_code_bloc/qr_code_bloc.dart';
import 'package:leadrat/features/search_location/data/data_source/search_location_data_source.dart';
import 'package:leadrat/features/search_location/data/data_source/search_location_data_source_impl.dart';
import 'package:leadrat/features/search_location/data/repository/search_location_repository_impl.dart';
import 'package:leadrat/features/search_location/domain/repository/search_location_repository.dart';
import 'package:leadrat/features/search_location/domain/usecase/search_location_use_case.dart';
import 'package:leadrat/features/search_location/presentation/bloc/search_location_bloc.dart';
import 'package:leadrat/features/settings/data/repository/settings_repository_impl.dart';
import 'package:leadrat/features/task_module/data/data_source/task_remote_data_source.dart';
import 'package:leadrat/features/task_module/data/data_source/task_remote_data_source_impl.dart';
import 'package:leadrat/features/task_module/data/repository/task_repository_impl.dart';
import 'package:leadrat/features/task_module/domain/repository/tasks_repository.dart';
import 'package:leadrat/features/task_module/domain/usecases/add_task_usecase.dart';
import 'package:leadrat/features/task_module/domain/usecases/delete_task_usecase.dart';
import 'package:leadrat/features/task_module/domain/usecases/get__task_on_schedule_date_usecase.dart';
import 'package:leadrat/features/task_module/domain/usecases/get_all_schedule_dates.dart';
import 'package:leadrat/features/task_module/domain/usecases/get_all_tasks_usecase.dart';
import 'package:leadrat/features/task_module/domain/usecases/get_task_details_usecase.dart';
import 'package:leadrat/features/task_module/domain/usecases/update_task_usecase.dart';
import 'package:leadrat/features/task_module/presentation/bloc/add_task_bloc/add_task_bloc.dart';
import 'package:leadrat/features/task_module/presentation/bloc/manage_tasks_bloc/manage_task_bloc.dart';
import 'package:leadrat/features/task_module/presentation/bloc/task_details_bloc/task_details_bloc.dart';
import 'package:leadrat/features/user_profile/data/data_source/remote/user_profile_remote_data_source.dart';
import 'package:leadrat/features/user_profile/data/data_source/remote/user_profile_remote_data_source_impl.dart';
import 'package:leadrat/features/user_profile/data/repository/user_profile_repository_impl.dart';
import 'package:leadrat/features/user_profile/domain/repository/user_profile_repository.dart';
import 'package:leadrat/features/user_profile/domain/usecase/change_password_usecase.dart';
import 'package:leadrat/features/user_profile/domain/usecase/delete_document_usecase.dart';
import 'package:leadrat/features/user_profile/domain/usecase/get_user_details_usecase.dart';
import 'package:leadrat/features/user_profile/domain/usecase/update_document_usecase.dart';
import 'package:leadrat/features/user_profile/domain/usecase/update_user_profile_use_case.dart';
import 'package:leadrat/features/user_profile/presentation/bloc/add_experience_document/add_experience_document_bloc.dart';
import 'package:leadrat/features/user_profile/presentation/bloc/change_password/change_password_bloc.dart';
import 'package:leadrat/features/user_profile/presentation/bloc/edit_basic_info/edit_basic_info_bloc.dart';
import 'package:leadrat/features/user_profile/presentation/bloc/user_profile/user_profile_bloc.dart';
import 'package:leadrat/features/whatsapp/data/data_source/remote/whatsapp_remote_data_source.dart';
import 'package:leadrat/features/whatsapp/data/data_source/remote/whatsapp_remote_data_source_impl.dart';
import 'package:leadrat/features/whatsapp/data/repository/whatsapp_repository_impl.dart';
import 'package:leadrat/features/whatsapp/domain/repository/whatsapp_repository.dart';
import 'package:leadrat/features/whatsapp/domain/usecase/get_all_whatsapp_messages_usecase.dart';
import 'package:leadrat/features/whatsapp/domain/usecase/get_whatsapp_message_templates.dart';
import 'package:leadrat/features/whatsapp/domain/usecase/twenty_four_hour_validation_usecase.dart';
import 'package:leadrat/features/whatsapp/presentation/bloc/crm_templates_bloc/crm_templates_bloc.dart';
import 'package:leadrat/features/whatsapp/presentation/bloc/whatsapp_chat/whatsapp_chat_bloc.dart';
import 'package:leadrat/features/whatsapp/presentation/bloc/whatsapp_media_preview_bloc/whatsapp_media_preview_bloc.dart';

import '../../features/auth/presentation/bloc/auth_login_bloc/auth_login_bloc.dart';
import '../../features/lead/data/data_source/local/leads_local_data_source.dart';
import '../../features/lead/data/data_source/local/leads_local_data_source_impl.dart';
import '../../features/lead/domain/usecase/booked_lead_use_case.dart';
import '../../features/lead/domain/usecase/get_booked_lead_usecase.dart';
import '../../features/lead/presentation/bloc/booking_form_bloc/booking_form_bloc.dart';
import '../../features/notification/domain/usecase/get_notification_count_usecase.dart';
import '../../features/properties/data/data_source/properties_remote_data_source.dart';
import '../../features/properties/data/data_source/properties_remote_data_source_impl.dart';
import '../../features/properties/data/repository/properties_repository_imp.dart';
import '../../features/properties/domain/repository/properties_repository.dart';
import '../../features/properties/domain/usecase/get_all_properties_usecase.dart';
import '../../features/properties/domain/usecase/get_property_templates_use_case.dart';
import '../../features/properties/presentation/bloc/manage_property_bloc/manage_property_bloc.dart';
import '../../features/settings/data/data_source/remote/settings_remote_data_source.dart';
import '../../features/settings/data/data_source/remote/settings_remote_data_source_impl.dart';
import '../../features/settings/domain/repository/settings_repository.dart';
import '../../features/settings/domain/use_cases/get_last_modeified_date_usecase.dart';
import '../../features/settings/domain/use_cases/get_user_details_usecase.dart';
import '../../features/settings/domain/use_cases/relink_usecase.dart';
import '../../features/settings/domain/use_cases/sync_master_data_usecase.dart';
import '../../features/settings/presentation/bloc/setting_sync/setting_sync_bloc.dart';
import '../../features/settings/presentation/bloc/settings/settings_bloc.dart';
import '../../features/user_profile/domain/usecase/upload_document_usecase.dart';
import '../../features/user_profile/presentation/bloc/user_document/user_document_bloc.dart';
import '../common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import '../common/data/custom_amenities_and_attributes/data_source/remote/custom_amenities_and_attributes_data_source.dart';
import '../common/data/user/data_source/local/user_local_data_source.dart';
import '../common/data/user/data_source/local/user_local_data_source_impl.dart';
import '../common/data/user/data_source/remote/user_remote_data_source.dart';
import '../common/data/user/data_source/remote/user_remote_data_source_impl.dart';
import '../common/data/user/repository/users_repository.dart';
import '../common/data/user/repository/users_repository_impl.dart';
import '../managers/shared_preference_manager/shared_preference_manager_impl.dart';
import '../services/local_storage_service/hive_service_impl.dart';
import '../services/local_storage_service/local_storage_service.dart';
import '../services/native_implementation_service/native_implementation_service.dart';
import '../services/native_implementation_service/native_implementation_service_impl.dart';
import '../services/call_detection_status_service/call_detection_status_service.dart';
import '../../features/lead/presentation/services/auto_dial_service.dart';

part 'di.dart';
