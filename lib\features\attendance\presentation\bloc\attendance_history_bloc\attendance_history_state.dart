part of 'attendance_history_bloc.dart';

@immutable
class AttendanceHistoryState {
  final PageState pageState;
  final List<AttendanceLogsListModel?>? attendanceHistoryList;
  final int currentPage;
  final bool hasMoreData;
  final bool isLoadingMore;
  final AttendanceHistoryParams? currentFilter;
  final String? emptyStateMessage;

  const AttendanceHistoryState({
    this.pageState = PageState.initial,
    this.attendanceHistoryList,
    this.currentPage = 1,
    this.hasMoreData = true,
    this.isLoadingMore = false,
    this.currentFilter,
    this.emptyStateMessage,
  });

  AttendanceHistoryState copyWith({
    PageState? pageState,
    List<AttendanceLogsListModel?>? attendanceHistoryList,
    int? currentPage,
    bool? hasMoreData,
    bool? isLoadingMore,
    AttendanceHistoryParams? currentFilter,
    String? emptyStateMessage,
  }) {
    return AttendanceHistoryState(
      pageState: pageState ?? this.pageState,
      attendanceHistoryList: attendanceHistoryList ?? this.attendanceHistoryList,
      currentPage: currentPage ?? this.currentPage,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      currentFilter: currentFilter ?? this.currentFilter,
      emptyStateMessage: emptyStateMessage ?? this.emptyStateMessage,
    );
  }
}
