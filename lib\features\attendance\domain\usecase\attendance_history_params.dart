import 'package:leadrat/core_main/enums/common/date_range.dart';

class AttendanceHistoryParams {
  final int pageNumber;
  final int pageSize;
  final String? fromDate;
  final String? toDate;
  final DateRange? dateRange;

  const AttendanceHistoryParams({
    this.pageNumber = 1,
    this.pageSize = 10,
    this.fromDate,
    this.toDate,
    this.dateRange,
  });

  AttendanceHistoryParams copyWith({
    int? pageNumber,
    int? pageSize,
    String? fromDate,
    String? toDate,
    DateRange? dateRange,
  }) {
    return AttendanceHistoryParams(
      pageNumber: pageNumber ?? this.pageNumber,
      pageSize: pageSize ?? this.pageSize,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      dateRange: dateRange ?? this.dateRange,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttendanceHistoryParams &&
        other.pageNumber == pageNumber &&
        other.pageSize == pageSize &&
        other.fromDate == fromDate &&
        other.toDate == toDate &&
        other.dateRange == dateRange;
  }

  @override
  int get hashCode {
    return pageNumber.hashCode ^
        pageSize.hashCode ^
        fromDate.hashCode ^
        toDate.hashCode ^
        dateRange.hashCode;
  }

  @override
  String toString() {
    return 'AttendanceHistoryParams(pageNumber: $pageNumber, pageSize: $pageSize, fromDate: $fromDate, toDate: $toDate, dateRange: $dateRange)';
  }
}
