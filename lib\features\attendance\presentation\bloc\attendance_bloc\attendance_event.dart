part of 'attendance_bloc.dart';

@immutable
sealed class AttendanceEvent {}

final class GetAttendanceSettingsEvent extends AttendanceEvent {}

final class GetTodayAttendanceLogEvent extends AttendanceEvent {}

final class ToggleClockInEvent extends AttendanceEvent {
  final bool? isClockedIn;

  ToggleClockInEvent({this.isClockedIn});
}

final class PunchLocationEvent extends AttendanceEvent {
  final String? comment;

  PunchLocationEvent({this.comment});
}

final class RefreshWorkingTimeEvent extends AttendanceEvent {}

final class GetAllAttendanceHistoryEvent extends AttendanceEvent {}

final class ApplyAttendanceFilterEvent extends AttendanceEvent {
  final AttendanceHistoryParams params;

  ApplyAttendanceFilterEvent(this.params);
}

final class GetGreetingAndAnimationEvent extends AttendanceEvent {}
