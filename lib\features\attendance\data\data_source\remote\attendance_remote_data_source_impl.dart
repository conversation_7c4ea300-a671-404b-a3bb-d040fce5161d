import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/features/attendance/data/data_source/remote/attendance_remote_data_source.dart';
import 'package:leadrat/features/attendance/data/models/attendance_logs_list_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_settings_model.dart';

import '../../models/attendance_post_model.dart';

class AttendanceRemoteDataSourceImpl extends LeadratRestService implements AttendanceRemoteDataSource {
  @override
  Future<List<AttendanceLogsListModel>?> getAttendanceHistory({
    int pageNumber = 1,
    int pageSize = 10,
    String? fromDate,
    String? toDate,
  }) async {
    final restRequest = createGetRequest(AttendanceRestResources.getAttendanceHistory(
      pageNumber: pageNumber,
      pageSize: pageSize,
      fromDate: fromDate,
      toDate: toDate,
    ));
    final response = await executeRequestAsync<PagedResponse<AttendanceLogsListModel, String>>(
        restRequest,
        (json) => PagedResponse<AttendanceLogsListModel, String>.fromJson(
              json,
              (data) => fromJsonObject(data, AttendanceLogsListModel.fromJson),
              (json) => json as String,
            ));

    return response.items;
  }

  @override
  Future<List<AttendanceModel>?> getAttendanceLogByUser(String userid, String timeZone) async {
    try {
      final restRequest = createGetRequest(AttendanceRestResources.getAttendanceLogByUser(userid, timeZone));
      final response = await executeRequestAsync<ResponseWrapper<List<AttendanceModel>>>(
        restRequest,
        (json) => ResponseWrapper<List<AttendanceModel>>.fromJson(
          json,
          (data) => fromJsonList(data, AttendanceModel.fromJson),
        ),
      );

      return response.data;
    } catch (e) {
      // Toast
      return null;
    }
  }

  @override
  Future<AttendanceSettingsModel?> getAttendanceSettings() async {
    final restRequest = createGetRequest(AttendanceRestResources.getAttendanceSettings());
    final response = await executeRequestAsync<ResponseWrapper<AttendanceSettingsModel>>(
      restRequest,
      (json) => ResponseWrapper<AttendanceSettingsModel>.fromJson(
        json,
        (data) => fromJsonObject(data, AttendanceSettingsModel.fromJson),
      ),
    );

    return response.data;
  }

  @override
  Future<String?> postClockInAttendance(AttendancePostModel clockInModel) async {
    final restRequest = createPostRequest(AttendanceRestResources.postClockInAttendance(), body: clockInModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String>>(
      restRequest,
      (json) => ResponseWrapper<String>.fromJson(json, (data) => data as String),
    );
    return response.data;
  }

  @override
  Future<bool?> postClockOutAttendance(AttendancePostModel clockOutModel) async {
    final restRequest = createPostRequest(AttendanceRestResources.postClockOutAttendance(), body: clockOutModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<AttendanceModel>?> getTodaysClockInsByWithTimeZone(String userId, String timeZoneId, String baseUtcOffset, String startTime) async {
    final restRequest = createGetRequest(AttendanceRestResources.getAttendanceLogBasedOnTimeZoneByUserId(userId, timeZoneId, baseUtcOffset, startTime));
    final response = await executeRequestAsync<ResponseWrapper<List<AttendanceModel>>>(
      restRequest,
      (json) => ResponseWrapper<List<AttendanceModel>>.fromJson(
        json,
        (data) => fromJsonList(data, AttendanceModel.fromJson),
      ),
    );
    return response.data;
  }
}
