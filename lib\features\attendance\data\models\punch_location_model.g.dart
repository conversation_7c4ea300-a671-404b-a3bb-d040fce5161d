// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'punch_location_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PunchLocationModel _$PunchLocationModelFromJson(Map<String, dynamic> json) =>
    PunchLocationModel(
      id: json['id'] as String?,
      clockInTime: json['clockInTime'] == null
          ? null
          : DateTime.parse(json['clockInTime'] as String),
      clockOutTime: json['clockOutTime'] == null
          ? null
          : DateTime.parse(json['clockOutTime'] as String),
      clockInLatitude: (json['clockInLatitude'] as num?)?.toDouble(),
      clockInLongitude: (json['clockInLongitude'] as num?)?.toDouble(),
      clockInLocation: json['clockInLocation'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$PunchLocationModelToJson(PunchLocationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'clockInTime': instance.clockInTime?.toIso8601String(),
      'clockOutTime': instance.clockOutTime?.toIso8601String(),
      'clockInLatitude': instance.clockInLatitude,
      'clockInLongitude': instance.clockInLongitude,
      'clockInLocation': instance.clockInLocation,
      'message': instance.message,
    };
