// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceFilterModel _$AttendanceFilterModelFromJson(
        Map<String, dynamic> json) =>
    AttendanceFilterModel(
      dateRange: $enumDecodeNullable(_$DateRangeEnumMap, json['DateRange']),
      fromDate: json['FromDate'] as String?,
      toDate: json['ToDate'] as String?,
    );

Map<String, dynamic> _$AttendanceFilterModelToJson(
        AttendanceFilterModel instance) =>
    <String, dynamic>{
      'DateRange': _$DateRangeEnumMap[instance.dateRange],
      'FromDate': instance.fromDate,
      'ToDate': instance.toDate,
    };

const _$DateRangeEnumMap = {
  DateRange.today: 0,
  DateRange.yesterday: 1,
  DateRange.lastSevenDays: 2,
  DateRange.lastTwentyEightDays: 3,
  DateRange.customDate: 4,
};
