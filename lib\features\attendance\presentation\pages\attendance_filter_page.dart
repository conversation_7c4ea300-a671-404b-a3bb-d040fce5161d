import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_date_picker.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';

import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import '../bloc/attendance_filter_bloc/attendance_filter_bloc.dart';

class AttendanceFilterPage extends LeadratStatefulWidget {
  final DateRange? selectedDateRange;

  const AttendanceFilterPage({
    super.key,
    this.selectedDateRange,
  });

  @override
  State<AttendanceFilterPage> createState() => _AttendanceFilterPageState();
}

class _AttendanceFilterPageState extends LeadratState<AttendanceFilterPage> {
  late AttendanceFilterBloc _attendanceFilterBloc;

  @override
  void initState() {
    super.initState();
    _attendanceFilterBloc = AttendanceFilterBloc();
    _attendanceFilterBloc.add(InitializeAttendanceFilterEvent(
      selectedDateRange: widget.selectedDateRange ?? DateRange.today,
    ));
  }

  @override
  void dispose() {
    _attendanceFilterBloc.close();
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return BlocProvider.value(
      value: _attendanceFilterBloc,
      child: Scaffold(
        backgroundColor: ColorPalette.primaryDarkColor,
        body: BlocBuilder<AttendanceFilterBloc, AttendanceFilterState>(
          builder: (context, state) {
            return Column(
              children: [
                Container(
                  decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(ImageResources.imageAppBarPattern), alignment: Alignment.bottomRight)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Text("filter", style: LexendTextStyles.lexend18Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      ),
                      GestureDetector(
                          onTap: () => _attendanceFilterBloc.add(ResetAttendanceFilterEvent()),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Text("reset", style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                          )),
                    ],
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Date Range',
                          style: LexendTextStyles.lexend14SemiBold.copyWith(
                            color: ColorPalette.white,
                          ),
                        ),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                _buildDateRangeList(context, state),
                                if (state.selectedDateRange == DateRange.customDate) _buildCustomDateSelectors(context, state),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.primaryDarkColor,
                    border: Border(
                      top: BorderSide(
                        color: ColorPalette.tertiaryTextColor.withValues(alpha: 0.3),
                        width: 0.8,
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: LeadratFormButton(
                              onPressed: () => Navigator.of(context).pop(),
                              buttonText: "Close",
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: LeadratFormButton(
                              onPressed: () {
                                Navigator.of(context).pop(_buildFilterResult(state));
                              },
                              buttonText: "Apply",
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDateRangeList(BuildContext context, AttendanceFilterState state) {
    const dateRanges = DateRange.values;

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: dateRanges.length,
      itemBuilder: (context, index) {
        final dateRange = dateRanges[index];
        return Column(
          children: [
            RadioListTile<DateRange>(
              key: ValueKey(index),
              value: dateRange,
              groupValue: state.selectedDateRange,
              onChanged: (value) {
                if (value != null) {
                  _attendanceFilterBloc.add(SelectDateRangeEvent(value));
                }
              },
              title: Text(
                dateRange.description,
                style: LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              activeColor: ColorPalette.primaryGreen,
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
              visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
            ),
            if (index < dateRanges.length - 1)
              Divider(
                color: ColorPalette.tertiaryTextColor.withValues(alpha: 0.2),
                height: 1,
              ),
          ],
        );
      },
    );
  }

  Widget _buildCustomDateSelectors(BuildContext context, AttendanceFilterState state) {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorPalette.eerieBlack.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ColorPalette.tertiaryTextColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Custom Date Range',
            style: LexendTextStyles.lexend14SemiBold.copyWith(
              color: ColorPalette.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'From Date',
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    LeadratDatePicker(
                      selectedDate: state.selectedFromDate,
                      placeholder: "Select from date",
                      placeHolderTextColor: ColorPalette.white,
                      onDateSelected: (date) {
                        _attendanceFilterBloc.add(SelectFromDateEvent(date));
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'To Date',
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    LeadratDatePicker(
                      selectedDate: state.selectedToDate,
                      placeholder: "Select to date",
                      placeHolderTextColor: ColorPalette.white,
                      onDateSelected: (date) {
                        _attendanceFilterBloc.add(SelectToDateEvent(date));
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _buildFilterResult(AttendanceFilterState state) {
    return {
      'dateRange': state.selectedDateRange,
      'fromDate': state.selectedFromDate,
      'toDate': state.selectedToDate,
    };
  }
}
