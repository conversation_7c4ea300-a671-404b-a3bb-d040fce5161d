import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import '../bloc/attendance_filter_bloc/attendance_filter_bloc.dart';

class AttendanceFilterPage extends LeadratStatefulWidget {
  final DateRange? selectedDateRange;

  const AttendanceFilterPage({
    super.key,
    this.selectedDateRange,
  });

  @override
  State<AttendanceFilterPage> createState() => _AttendanceFilterPageState();
}

class _AttendanceFilterPageState extends LeadratState<AttendanceFilterPage> {
  late AttendanceFilterBloc _attendanceFilterBloc;

  @override
  void initState() {
    super.initState();
    _attendanceFilterBloc = AttendanceFilterBloc();
    _attendanceFilterBloc.add(InitializeAttendanceFilterEvent(
      selectedDateRange: widget.selectedDateRange ?? DateRange.today,
    ));
  }

  @override
  void dispose() {
    _attendanceFilterBloc.close();
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return BlocProvider.value(
      value: _attendanceFilterBloc,
      child: Scaffold(
        backgroundColor: ColorPalette.primaryDarkColor,
        appBar: AppBar(
          backgroundColor: ColorPalette.primaryDarkColor,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: ColorPalette.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            'Filter',
            style: LexendTextStyles.lexend16SemiBold.copyWith(
              color: ColorPalette.white,
            ),
          ),
          centerTitle: true,
        ),
        body: BlocBuilder<AttendanceFilterBloc, AttendanceFilterState>(
          builder: (context, state) {
            return Column(
              children: [
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Date Range',
                          style: LexendTextStyles.lexend14SemiBold.copyWith(
                            color: ColorPalette.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: _buildDateRangeList(context, state),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.primaryDarkColor,
                    border: Border(
                      top: BorderSide(
                        color: ColorPalette.tertiaryTextColor.withValues(alpha: 0.3),
                        width: 0.8,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: LeadratFormButton(
                          onPressed: () => Navigator.of(context).pop(),
                          buttonText: "Close",
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: LeadratFormButton(
                          onPressed: () {
                            Navigator.of(context).pop(state.selectedDateRange);
                          },
                          buttonText: "Apply",
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDateRangeList(BuildContext context, AttendanceFilterState state) {
    final dateRanges = DateRange.values;

    return ListView.builder(
      itemCount: dateRanges.length,
      itemBuilder: (context, index) {
        final dateRange = dateRanges[index];
        return Column(
          children: [
            RadioListTile<DateRange>(
              key: ValueKey(index),
              value: dateRange,
              groupValue: state.selectedDateRange,
              onChanged: (value) {
                if (value != null) {
                  _attendanceFilterBloc.add(SelectDateRangeEvent(value));
                }
              },
              title: Text(
                dateRange.description,
                style: LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              activeColor: ColorPalette.primaryGreen,
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
              visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
            ),
            if (index < dateRanges.length - 1)
              Divider(
                color: ColorPalette.tertiaryTextColor.withValues(alpha: 0.2),
                height: 1,
              ),
          ],
        );
      },
    );
  }
}
