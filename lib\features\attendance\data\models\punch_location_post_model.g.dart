// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'punch_location_post_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PunchLocationPostModel _$PunchLocationPostModelFromJson(
        Map<String, dynamic> json) =>
    PunchLocationPostModel(
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      locationName: json['locationName'] as String?,
      subLocality: json['subLocality'] as String?,
      locality: json['locality'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      comment: json['comment'] as String?,
      attendanceId: json['attendanceId'] as String?,
    );

Map<String, dynamic> _$PunchLocationPostModelToJson(
        PunchLocationPostModel instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'locationName': instance.locationName,
      'subLocality': instance.subLocality,
      'locality': instance.locality,
      'city': instance.city,
      'state': instance.state,
      'comment': instance.comment,
      'attendanceId': instance.attendanceId,
    };
