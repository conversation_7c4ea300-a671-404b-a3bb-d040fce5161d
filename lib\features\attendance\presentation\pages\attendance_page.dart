import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/features/attendance/presentation/bloc/attendance_bloc/attendance_bloc.dart';
import 'package:leadrat/features/attendance/presentation/pages/attendance_filter_page.dart';
import 'package:leadrat/features/attendance/domain/usecase/attendance_history_params.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:lottie/lottie.dart';

import '../../../../core_main/common/base/presentation/leadrat_statefull_widget.dart';
import '../../../../core_main/common/widgets/current_time_widget.dart';
import '../../../../core_main/resources/theme/color_palette.dart';
import '../../../../core_main/resources/theme/text_styles.dart';
import '../../../../core_main/utilities/dialog_manager.dart';
import '../../../../core_main/utilities/leadrat_custom_snackbar.dart';
import '../widgets/attendance_action_buttons_widget.dart';
import '../widgets/attendance_header_widget.dart';
import '../widgets/working_time_widget.dart';

class AttendancePage extends LeadratStatefulWidget {
  const AttendancePage({super.key});

  @override
  State<AttendancePage> createState() => _AttendanceScreen();
}

class _AttendanceScreen extends LeadratState<AttendancePage> with SingleTickerProviderStateMixin {
  DateRange selectedDateRange = DateRange.today;

  @override
  void initState() {
    super.initState();

    context.read<AttendanceBloc>().add(GetAttendanceSettingsEvent());
    context.read<AttendanceBloc>().add(GetTodayAttendanceLogEvent());
    context.read<AttendanceBloc>().add(GetAllAttendanceHistoryEvent());
    context.read<AttendanceBloc>().add(GetGreetingAndAnimationEvent());
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocConsumer<AttendanceBloc, AttendanceState>(
          listener: (context, state) {
            if (state.clockedInState == AttendanceLogInState.clockingIn) {
              DialogManager().showTransparentProgressDialog(context, message: "Clocking In");
            } else if (state.clockedInState == AttendanceLogInState.clockingOut) {
              DialogManager().showTransparentProgressDialog(context, message: "Clocking Out");
            } else if (state.clockedInState == AttendanceLogInState.success) {
              DialogManager().hideTransparentProgressDialog();
            } else if (state.clockedInState == AttendanceLogInState.successfullyClockedIn) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: "Clock in successfully done");
            } else if (state.clockedInState == AttendanceLogInState.successfullyClockedOut) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: "Clock out successfully done");
            } else if (state.clockedInState == AttendanceLogInState.punchLocationSuccess) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: "Location punched successfully");
            } else if (state.clockedInState == AttendanceLogInState.errorInClockingIn) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "Clock in was unsuccessful");
            } else if (state.clockedInState == AttendanceLogInState.errorInClockingOut) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "Clock out was unsuccessful");
            } else if (state.clockedInState == AttendanceLogInState.punchLocationError) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "Failed to record location. Please try again.");
            } else if (state.clockedInState == AttendanceLogInState.errorInLoadingAttendance) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "There was some problem in loading the attendance");
            } else if (state.clockedInState == AttendanceLogInState.selfieIsMandatory) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "Selfie is mandatory to mark the attendance");
            } else if (state.clockedInState == AttendanceLogInState.locationMandatory) {
              DialogManager().hideTransparentProgressDialog();
              LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "location is mandatory to mark the attendance");
            }
          },
          builder: (context, state) {
            /* if (state.clockedInState == AttendanceLogInState.loading) {
              return const AttendanceScreenSkeletonLoader();
            }*/
            return Container(
              color: Colors.black,
              child: Stack(
                children: [
                  Image.asset(
                    ImageResources.imageListingPageBackgroundPattern,
                    height: context.height(100),
                    width: context.width(100),
                    fit: BoxFit.cover,
                  ),
                  Column(
                    children: [
                      Stack(
                        children: [
                          const AttendanceHeaderWidget(),
                          Padding(
                            padding: const EdgeInsets.all(10),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const SizedBox(height: 80),
                                Container(
                                  decoration: const BoxDecoration(
                                    color: ColorPalette.eerieBlack,
                                    borderRadius: BorderRadius.all(Radius.circular(16)),
                                  ),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        right: 1,
                                        top: 10,
                                        child: Container(
                                          margin: const EdgeInsets.all(10),
                                          height: 80,
                                          width: 80,
                                          child: ClipOval(
                                            child: Lottie.asset(
                                              state.partOfTheDayAnimation ?? LottieResources.goodMorningAnimation,
                                              repeat: true,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(left: 16, bottom: 0, top: 15, right: 16),
                                            child: Row(
                                              crossAxisAlignment: CrossAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(ImageResources.iconCalendar),
                                                const SizedBox(width: 8),
                                                Text(
                                                  "Attendance",
                                                  style: LexendTextStyles.lexend14Regular.copyWith(
                                                    color: ColorPalette.gray400,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const CurrentTimeWidget(
                                            shiftStartTime: '09:00',
                                            shiftEndTime: '05:00',
                                            showShiftTime: true,
                                          ),
                                          const Divider(color: ColorPalette.lightBackground),
                                          AttendanceActionButtonsWidget(
                                            isClockedIn: state.clockInOutItem?.isClockedIn ?? false,
                                            isPunchingLocation: state.isPunchingLocation,
                                            showPunchLocationButton: state.isGeoFenceEnabled ?? false,
                                            onClockIn: () {
                                              context.read<AttendanceBloc>().add(ToggleClockInEvent(isClockedIn: false));
                                            },
                                            onClockOut: () {
                                              context.read<AttendanceBloc>().add(ToggleClockInEvent(isClockedIn: true));
                                            },
                                            onPunchLocation: (String? comment) {
                                              context.read<AttendanceBloc>().add(PunchLocationEvent(comment: comment));
                                            },
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      // Conditional punch locations widget
                      // if (state.clockInOutItem?.isClockedIn ?? false) ...[
                      //   const SizedBox(height: 16),
                      //   PunchLocationsListWidget(
                      //     punchLocations: state.punchLocations,
                      //     onSeeLocations: () {
                      //       Navigator.of(context).push(
                      //         MaterialPageRoute(
                      //           builder: (context) => const AttendanceHistoryPage(),
                      //         ),
                      //       );
                      //     },
                      //   ),
                      // ],

                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.all(8),
                          decoration: const BoxDecoration(
                            color: ColorPalette.eerieBlack,
                            borderRadius: BorderRadius.all(Radius.circular(16)),
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 16, bottom: 10, top: 10, right: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Attendance log",
                                      style: LexendTextStyles.lexend15Medium.copyWith(
                                        color: ColorPalette.white,
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () => _openFilterPage(),
                                      child: Row(
                                        children: [
                                          Text(
                                            selectedDateRange.description,
                                            style: LexendTextStyles.lexend12Regular.copyWith(
                                              color: ColorPalette.white,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          const Icon(
                                            Icons.keyboard_arrow_down,
                                            color: ColorPalette.white,
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(color: ColorPalette.lightBackground),
                              Expanded(
                                child: state.attendanceHistoryList?.isEmpty ?? true
                                    ? const Center(
                                        child: Text(
                                          "No attendance logs available",
                                          style: TextStyle(color: Colors.grey),
                                        ),
                                      )
                                    : ListView.builder(
                                        padding: const EdgeInsets.only(bottom: 16),
                                        itemCount: state.attendanceHistoryList?.length ?? 0,
                                        itemBuilder: (context, index) {
                                          final data = state.attendanceHistoryList![index];
                                          return Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 15),
                                                child: Text(
                                                  DateFormat('EEE, MMM dd, yyyy').format(data?.fromDate ?? DateTime.now()),
                                                  style: LexendTextStyles.lexend12Regular.copyWith(
                                                    color: ColorPalette.primaryWhite300TextColor,
                                                  ),
                                                ),
                                              ),
                                              if (data?.logs?.isNotEmpty ?? false)
                                                ...data!.logs!.map((log) {
                                                  return WorkingTimeWidget(
                                                    log: log,
                                                  );
                                                }).toList()
                                              else
                                                Padding(
                                                  padding: const EdgeInsets.all(8.0),
                                                  child: Text(
                                                    "No logs available for this date",
                                                    style: LexendTextStyles.lexend12Regular.copyWith(
                                                      color: ColorPalette.gray300,
                                                    ),
                                                  ),
                                                ),
                                              if (index < (state.attendanceHistoryList?.length ?? 0) - 1) const SizedBox(height: 12),
                                            ],
                                          );
                                        },
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _openFilterPage() async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => AttendanceFilterPage(
          selectedDateRange: selectedDateRange,
        ),
      ),
    );

    if (result != null) {
      final newDateRange = result['dateRange'] as DateRange?;
      final fromDate = result['fromDate'] as DateTime?;
      final toDate = result['toDate'] as DateTime?;

      if (newDateRange != null && newDateRange != selectedDateRange) {
        setState(() {
          selectedDateRange = newDateRange;
        });

        // Apply the filter to attendance history
        _applyAttendanceFilter(newDateRange, fromDate, toDate);
      }
    }
  }

  void _applyAttendanceFilter(DateRange dateRange, DateTime? fromDate, DateTime? toDate) {
    // Create attendance history params based on the selected filter
    AttendanceHistoryParams params;

    if (dateRange == DateRange.customDate && fromDate != null && toDate != null) {
      // Custom date range
      final fromDateStr = DateTime(fromDate.year, fromDate.month, fromDate.day).toUniversalTimeStartOfDay().toString();
      final toDateStr = DateTime(toDate.year, toDate.month, toDate.day, 23, 59, 59).toUtc().toString();

      params = AttendanceHistoryParams(
        pageNumber: 1,
        pageSize: 10,
        fromDate: fromDateStr,
        toDate: toDateStr,
        dateRange: dateRange,
      );
    } else {
      // Use predefined date range
      params = _createParamsFromDateRange(dateRange);
    }

    // Apply filter to the attendance bloc to update the logs shown on this page
    context.read<AttendanceBloc>().add(ApplyAttendanceFilterEvent(params));
  }

  AttendanceHistoryParams _createParamsFromDateRange(DateRange dateRange) {
    final today = DateTime.now().toUserTimeZone()!;
    String fromDate;
    String toDate;

    switch (dateRange) {
      case DateRange.today:
        fromDate = DateTime(today.year, today.month, today.day).toUniversalTimeStartOfDay().toString();
        toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        break;
      case DateRange.yesterday:
        final yesterday = today.subtract(const Duration(days: 1));
        fromDate = DateTime(yesterday.year, yesterday.month, yesterday.day).toUniversalTimeStartOfDay().toString();
        toDate = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59).toUtc().toString();
        break;
      case DateRange.lastSevenDays:
        final sevenDaysAgo = today.subtract(const Duration(days: 7));
        fromDate = DateTime(sevenDaysAgo.year, sevenDaysAgo.month, sevenDaysAgo.day).toUniversalTimeStartOfDay().toString();
        toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        break;
      case DateRange.lastTwentyEightDays:
        final twentyEightDaysAgo = today.subtract(const Duration(days: 28));
        fromDate = DateTime(twentyEightDaysAgo.year, twentyEightDaysAgo.month, twentyEightDaysAgo.day).toUniversalTimeStartOfDay().toString();
        toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        break;
      case DateRange.customDate:
        // This case should not be reached here, but fallback to today
        fromDate = DateTime(today.year, today.month, today.day).toUniversalTimeStartOfDay().toString();
        toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        break;
    }

    return AttendanceHistoryParams(
      pageNumber: 1,
      pageSize: 10,
      fromDate: fromDate,
      toDate: toDate,
      dateRange: dateRange,
    );
  }
}
