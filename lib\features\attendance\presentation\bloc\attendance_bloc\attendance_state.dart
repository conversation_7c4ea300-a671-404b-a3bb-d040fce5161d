part of 'attendance_bloc.dart';

@immutable
class AttendanceState {
  final AttendanceLogInState clockedInState;
  final ClockInOutItem? clockInOutItem;
  final List<ClockInOutItem?>? attendanceList;
  final bool isPunchingLocation;
  final String? currentWorkingTime;
  final List<AttendanceLogsListModel?>? attendanceHistoryList;
  final String? partOfTheDayAnimation;
  final bool? isGeoFenceEnabled;
  final AttendanceHistoryParams? currentFilter;
  final String? emptyStateMessage;

  const AttendanceState({
    this.clockedInState = AttendanceLogInState.initial,
    this.clockInOutItem,
    this.attendanceList,
    this.isPunchingLocation = false,
    this.currentWorkingTime,
    this.attendanceHistoryList,
    this.partOfTheDayAnimation,
    this.isGeoFenceEnabled,
    this.currentFilter,
    this.emptyStateMessage,
  });

  AttendanceState copyWith({
    AttendanceLogInState? clockedInState,
    ClockInOutItem? clockInOutItem,
    List<ClockInOutItem?>? attendanceList,
    bool? isPunchingLocation,
    String? currentWorkingTime,
    List<AttendanceLogsListModel?>? attendanceHistoryList,
    String? partOfTheDayAnimation,
    bool? isGeoFenceEnabled,
    AttendanceHistoryParams? currentFilter,
    String? emptyStateMessage,
  }) {
    return AttendanceState(
      clockedInState: clockedInState ?? this.clockedInState,
      clockInOutItem: clockInOutItem ?? this.clockInOutItem,
      attendanceList: attendanceList ?? this.attendanceList,
      isPunchingLocation: isPunchingLocation ?? this.isPunchingLocation,
      currentWorkingTime: currentWorkingTime ?? this.currentWorkingTime,
      attendanceHistoryList: attendanceHistoryList ?? this.attendanceHistoryList,
      partOfTheDayAnimation: partOfTheDayAnimation ?? this.partOfTheDayAnimation,
      isGeoFenceEnabled: isGeoFenceEnabled ?? this.isGeoFenceEnabled,
      currentFilter: currentFilter ?? this.currentFilter,
      emptyStateMessage: emptyStateMessage ?? this.emptyStateMessage,
    );
  }
}

enum AttendanceLogInState {
  initial,
  loading,
  clockingIn,
  clockingOut,
  success,
  successfullyClockedIn,
  successfullyClockedOut,
  errorInClockingIn,
  errorInClockingOut,
  errorInLoadingAttendance,
  selfieIsMandatory,
  locationMandatory,
  notInProximity,
  punchingLocation,
  punchLocationSuccess,
  punchLocationError,
  loadingPunchLocations,
}
