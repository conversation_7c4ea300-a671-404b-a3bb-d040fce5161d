// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceModel _$AttendanceModelFromJson(Map<String, dynamic> json) =>
    AttendanceModel(
      clockInTime: json['clockInTime'] == null
          ? null
          : DateTime.parse(json['clockInTime'] as String),
      clockOutTime: json['clockOutTime'] == null
          ? null
          : DateTime.parse(json['clockOutTime'] as String),
      clockInLatitude: (json['clockInLatitude'] as num?)?.toDouble(),
      clockInLongitude: (json['clockInLongitude'] as num?)?.toDouble(),
      clockInLocation: json['clockInLocation'] as String?,
      clockOutLatitude: (json['clockOutLatitude'] as num?)?.toDouble(),
      clockOutLongitude: (json['clockOutLongitude'] as num?)?.toDouble(),
      clockOutLocation: json['clockOutLocation'] as String?,
      isClosed: json['isClosed'] as bool?,
      isSystemGenerated: json['isSystemGenerated'] as bool?,
      clockInImageUrl: json['clockInImageUrl'] as String?,
      clockOutImageUrl: json['clockOutImageUrl'] as String?,
      userId: json['userId'] as String?,
      createdBy: json['createdBy'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      deletedOn: json['deletedOn'] == null
          ? null
          : DateTime.parse(json['deletedOn'] as String),
      deletedBy: json['deletedBy'] as String?,
      id: json['id'] as String?,
      isDeleted: json['isDeleted'] as bool?,
      isPunchInLocation: json['isPunchInLocation'] as bool?,
    );

Map<String, dynamic> _$AttendanceModelToJson(AttendanceModel instance) =>
    <String, dynamic>{
      'clockInTime': instance.clockInTime?.toIso8601String(),
      'clockOutTime': instance.clockOutTime?.toIso8601String(),
      'clockInLatitude': instance.clockInLatitude,
      'clockInLongitude': instance.clockInLongitude,
      'clockInLocation': instance.clockInLocation,
      'clockOutLatitude': instance.clockOutLatitude,
      'clockOutLongitude': instance.clockOutLongitude,
      'clockOutLocation': instance.clockOutLocation,
      'isClosed': instance.isClosed,
      'isSystemGenerated': instance.isSystemGenerated,
      'clockInImageUrl': instance.clockInImageUrl,
      'clockOutImageUrl': instance.clockOutImageUrl,
      'userId': instance.userId,
      'createdBy': instance.createdBy,
      'createdOn': instance.createdOn?.toIso8601String(),
      'lastModifiedBy': instance.lastModifiedBy,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'deletedOn': instance.deletedOn?.toIso8601String(),
      'deletedBy': instance.deletedBy,
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'isPunchInLocation': instance.isPunchInLocation,
    };
