import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';

import '../../../../core_main/resources/common/image_resources.dart';
import '../../../../core_main/resources/theme/color_palette.dart';
import '../../../../core_main/resources/theme/text_styles.dart';
import '../../data/models/attendance_logs_list_model.dart';
import '../../data/models/attendance_logs_model.dart';
import '../bloc/attendance_history_bloc/attendance_history_bloc.dart';
import '../widgets/attendance_history_screen_skeleton_loader.dart';

class AttendanceHistoryPage extends StatefulWidget {
  const AttendanceHistoryPage({super.key});

  @override
  State<StatefulWidget> createState() => _AttendanceHistoryPageState();
}

class _AttendanceHistoryPageState extends State<AttendanceHistoryPage> {
  @override
  void initState() {
    super.initState();

    // Initialize with today's attendance logs by default
    context.read<AttendanceHistoryBloc>().add(GetAllAttendanceHistoryEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<AttendanceHistoryBloc, AttendanceHistoryState>(
          builder: (context, state) {
            if (state.pageState == PageState.loading) {
              return const AttendanceHistoryScreenSkeletonLoader();
            } else if (state.pageState == PageState.failure) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    state.emptyStateMessage ?? "Failed to load attendance history",
                    style: LexendTextStyles.lexend18SemiBold.copyWith(
                      color: ColorPalette.primaryTextColor,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            } else if (state.pageState == PageState.success && (state.attendanceHistoryList?.isEmpty ?? true)) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    state.emptyStateMessage ?? "No attendance history found",
                    style: LexendTextStyles.lexend18SemiBold.copyWith(
                      color: ColorPalette.primaryTextColor,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            }
            return LayoutBuilder(
              builder: (context, constraints) {
                return const Stack(
                  children: [
                    BackgroundImage(),
                    Column(
                      children: [
                        AttendanceHistoryHeader(),
                        Expanded(child: AttendanceList()),
                      ],
                    ),
                  ],
                );
              },
            );
          },
        ),
      ),
      backgroundColor: Colors.black,
    );
  }
}

class BackgroundImage extends StatelessWidget {
  const BackgroundImage({super.key});

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      ImageResources.imageListingPageBackgroundPattern,
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      fit: BoxFit.cover,
    );
  }
}

class AttendanceHistoryHeader extends StatelessWidget {
  const AttendanceHistoryHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: context.height(8),
      width: double.infinity,
      decoration: const BoxDecoration(
        color: ColorPalette.darkBlack,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
      ),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.bottomRight,
            child: SvgPicture.asset(
              ImageResources.imageBubblePattern,
              width: MediaQuery.of(context).size.width * 0.3, // 30% of screen width
              fit: BoxFit.cover,
            ),
          ),
          const Positioned(
            left: 16,
            bottom: 20,
            child: BackButtonWidget(),
          ),
          Positioned(
            bottom: 20,
            left: MediaQuery.of(context).size.width * 0.4 - 50, // Center the title
            child: Text(
              "Attendance History",
              style: LexendTextStyles.lexend18SemiBold.copyWith(
                color: ColorPalette.primaryTextColor,
                fontSize: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BackButtonWidget extends StatelessWidget {
  const BackButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Padding(
        padding: const EdgeInsets.only(left: 16.0),
        child: SvgPicture.asset(
          ImageResources.iconBackButtonCircle,
          width: 25,
          height: 25,
        ),
      ),
    );
  }
}

class AttendanceList extends StatefulWidget {
  const AttendanceList({super.key});

  @override
  State<AttendanceList> createState() => _AttendanceListState();
}

class _AttendanceListState extends State<AttendanceList> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      context.read<AttendanceHistoryBloc>().add(LoadMoreAttendanceHistoryEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceHistoryBloc, AttendanceHistoryState>(
      builder: (context, state) {
        final attendanceHistoryList = state.attendanceHistoryList ?? [];
        final itemCount = state.isLoadingMore ? attendanceHistoryList.length + 1 : attendanceHistoryList.length;

        return ListView.builder(
          controller: _scrollController,
          itemCount: itemCount,
          itemBuilder: (context, index) {
            if (index < attendanceHistoryList.length) {
              final data = attendanceHistoryList[index];
              return AttendanceItem(data: data);
            } else {
              // Loading indicator for pagination
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: CircularProgressIndicator(
                    color: ColorPalette.primaryGreen,
                  ),
                ),
              );
            }
          },
        );
      },
    );
  }
}

class AttendanceItem extends StatelessWidget {
  final AttendanceLogsListModel? data;

  const AttendanceItem({Key? key, required this.data}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ${DateFormat('dd MMM').format(data?.fromDate ?? DateTime(1))} - ${DateFormat('dd MMM').format(data?.toDate ?? DateTime(1))}',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16),
          ),
          // Add null safety for `data.logs`
          if (data?.logs != null || (data?.logs?.isNotEmpty ?? false))
            ...data!.logs!.map((record) => AttendanceLogItem(record: record)).toList()
          else
            // Optionally handle the case where `logs` is null
            const Text("No logs available", style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }
}

class AttendanceLogItem extends StatelessWidget {
  final AttendanceLogModel? record; // Replace with your actual data type

  const AttendanceLogItem({Key? key, required this.record}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Card(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // First column for Date
              SizedBox(
                width: context.width(10), // Fixed width for the first column
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      record?.clockInTime != null ? DateFormat("d").format(record?.clockInTime?.toUserTimeZone() ?? DateTime(1)) : "NA", // Day
                      style: LexendTextStyles.lexend18Regular.copyWith(
                        color: ColorPalette.primaryWhiteTextColor,
                        fontSize: 28,
                      ),
                    ),
                    Text(
                      record?.clockInTime != null ? DateFormat("MMM").format(record?.clockInTime?.toUserTimeZone() ?? DateTime(1)) : "--", // Month
                      style: LexendTextStyles.lexend18Regular.copyWith(
                        color: ColorPalette.primaryWhiteTextColor,
                      ),
                    ),
                  ],
                ),
              ),

              // Second column for "Started" time and location
              SizedBox(
                width: context.width(25), // Fixed width for the second column
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Started',
                      style: LexendTextStyles.lexend15Medium.copyWith(
                        color: ColorPalette.primaryWhiteTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      record?.clockInTime != null ? DateFormat('hh : mm a').format(record?.clockInTime?.toUserTimeZone() ?? DateTime(1)) : "--",
                      style: LexendTextStyles.lexend18SemiBold.copyWith(
                        color: ColorPalette.primaryWhiteTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      record?.clockInLocation ?? "--", // Display location or other info
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.primaryWhite300TextColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Text between "Started" and "Ended" columns
              SizedBox(
                width: context.width(20),
                child: Text(
                  record?.clockOutTime != null ? "${record?.clockOutTime?.toUserTimeZone()?.difference(record?.clockInTime?.toUserTimeZone() ?? DateTime(1)).inHours}h : ${record?.clockOutTime?.toUserTimeZone()?.difference(record?.clockInTime?.toUserTimeZone() ?? DateTime(1)).inMinutes.remainder(60)}m" : "", // Replace with any text you want to display
                  style: LexendTextStyles.lexend15Medium.copyWith(
                    color: ColorPalette.primaryWhiteTextColor,
                  ),
                ),
              ),

              // Third column for "Ended" time and location
              SizedBox(
                width: context.width(25), // Fixed width for the third column
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ended',
                      style: LexendTextStyles.lexend15Medium.copyWith(
                        color: ColorPalette.primaryWhiteTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      record?.clockOutTime != null ? DateFormat('hh : mm a').format(record?.clockOutTime?.toUserTimeZone() ?? DateTime(1)) : "--", // Display end time
                      style: LexendTextStyles.lexend18SemiBold.copyWith(
                        color: ColorPalette.primaryWhiteTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      record?.clockOutLocation ?? "--", // Display location or other info
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.primaryWhite300TextColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
