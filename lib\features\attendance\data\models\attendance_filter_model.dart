import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';

part 'attendance_filter_model.g.dart';

@JsonSerializable(explicitToJson: true)
class AttendanceFilterModel {
  @Json<PERSON>ey(name: "DateRange")
  final DateRange? dateRange;

  @J<PERSON><PERSON><PERSON>(name: "FromDate")
  final String? fromDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: "ToDate")
  final String? toDate;

  AttendanceFilterModel({
    this.dateRange,
    this.fromDate,
    this.toDate,
  });

  factory AttendanceFilterModel.fromJson(Map<String, dynamic> json) =>
      _$AttendanceFilterModelFromJson(json);

  Map<String, dynamic> toJson() => _$AttendanceFilterModelToJson(this);

  AttendanceFilterModel copyWith({
    DateRange? dateRange,
    String? fromDate,
    String? toDate,
    bool updateDateRange = true,
    bool clearFromDate = false,
    bool clearToDate = false,
  }) {
    return AttendanceFilterModel(
      dateRange: updateDateRange ? (dateRange ?? this.dateRange) : this.dateRange,
      fromDate: clearFromDate ? null : (fromDate ?? this.fromDate),
      toDate: clearToDate ? null : (toDate ?? this.toDate),
    );
  }

  String toQueryString() {
    String queryString = '';

    if (dateRange != null) {
      queryString += '&DateRange=${dateRange!.name}';
    }

    if (fromDate != null && fromDate!.isNotEmpty) {
      queryString += '&FromDate=$fromDate';
    }

    if (toDate != null && toDate!.isNotEmpty) {
      queryString += '&ToDate=$toDate';
    }


    return queryString.isNotEmpty ? queryString.substring(1) : '';
  }

  bool get isEmpty {
    return dateRange == null &&
        (fromDate == null || fromDate!.isEmpty) &&
        (toDate == null || toDate!.isEmpty);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AttendanceFilterModel &&
          runtimeType == other.runtimeType &&
          dateRange == other.dateRange &&
          fromDate == other.fromDate &&
          toDate == other.toDate;

  @override
  int get hashCode =>
      dateRange.hashCode ^
      fromDate.hashCode ^
      toDate.hashCode;
}
