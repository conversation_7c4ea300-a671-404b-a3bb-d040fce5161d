import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:leadrat/features/attendance/data/models/attendance_logs_list_model.dart';
import 'package:leadrat/features/attendance/domain/usecase/attendance_history_params.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';

import '../../../../../core_main/enums/app_enum/page_state_enum.dart';
import '../../../domain/usecase/attendance_history_usecase.dart';

part 'attendance_history_event.dart';

part 'attendance_history_state.dart';

class AttendanceHistoryBloc extends Bloc<AttendanceHistoryEvent, AttendanceHistoryState> {
  final AttendanceHistoryUseCase _attendanceHistoryUseCase;

  AttendanceHistoryBloc(
    this._attendanceHistoryUseCase,
  ) : super(const AttendanceHistoryState()) {
    on<GetAllAttendanceHistoryEvent>(_getAttendanceHistory);
    on<LoadMoreAttendanceHistoryEvent>(_loadMoreAttendanceHistory);
    on<ApplyAttendanceFilterEvent>(_applyAttendanceFilter);
  }

  FutureOr<void> _getAttendanceHistory(GetAllAttendanceHistoryEvent event, Emitter<AttendanceHistoryState> emit) async {
    emit(state.copyWith(pageState: PageState.loading));

    // Use provided params or create default params for today
    final params = event.params ?? _getDefaultTodayParams();

    var result = await _attendanceHistoryUseCase.call(params);
    result.fold((failure) {
      emit(state.copyWith(
        pageState: PageState.failure,
        emptyStateMessage: "Failed to load attendance history",
      ));
    }, (res) {
      final emptyMessage = _getEmptyStateMessage(params, res?.isEmpty ?? true);
      emit(state.copyWith(
        pageState: PageState.success,
        attendanceHistoryList: res,
        currentFilter: params,
        currentPage: 1,
        hasMoreData: (res?.length ?? 0) >= params.pageSize,
        emptyStateMessage: emptyMessage,
      ));
    });
  }

  FutureOr<void> _loadMoreAttendanceHistory(LoadMoreAttendanceHistoryEvent event, Emitter<AttendanceHistoryState> emit) async {
    if (state.isLoadingMore || !state.hasMoreData || state.currentFilter == null) return;

    emit(state.copyWith(isLoadingMore: true));

    final nextPageParams = state.currentFilter!.copyWith(
      pageNumber: state.currentPage + 1,
    );

    var result = await _attendanceHistoryUseCase.call(nextPageParams);
    result.fold((failure) {
      emit(state.copyWith(isLoadingMore: false));
    }, (res) {
      final updatedList = [...?state.attendanceHistoryList, ...?res];
      emit(state.copyWith(
        attendanceHistoryList: updatedList,
        currentPage: state.currentPage + 1,
        hasMoreData: (res?.length ?? 0) >= nextPageParams.pageSize,
        isLoadingMore: false,
      ));
    });
  }

  FutureOr<void> _applyAttendanceFilter(ApplyAttendanceFilterEvent event, Emitter<AttendanceHistoryState> emit) async {
    emit(state.copyWith(pageState: PageState.loading));

    var result = await _attendanceHistoryUseCase.call(event.params);
    result.fold((failure) {
      emit(state.copyWith(
        pageState: PageState.failure,
        emptyStateMessage: "Failed to load attendance history",
      ));
    }, (res) {
      final emptyMessage = _getEmptyStateMessage(event.params, res?.isEmpty ?? true);
      emit(state.copyWith(
        pageState: PageState.success,
        attendanceHistoryList: res,
        currentFilter: event.params,
        currentPage: 1,
        hasMoreData: (res?.length ?? 0) >= event.params.pageSize,
        emptyStateMessage: emptyMessage,
      ));
    });
  }

  AttendanceHistoryParams _getDefaultTodayParams() {
    final today = DateTime.now().toUserTimeZone();
    final fromDate = DateTime(today!.year, today.month, today.day).toUniversalTimeStartOfDay().toString();
    final toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();

    return AttendanceHistoryParams(
      pageNumber: 1,
      pageSize: 10,
      fromDate: fromDate,
      toDate: toDate,
      dateRange: DateRange.today,
    );
  }

  /// Helper method to convert DateRange and custom dates to AttendanceHistoryParams
  AttendanceHistoryParams createParamsFromFilter(DateRange dateRange, DateTime? customFromDate, DateTime? customToDate) {
    String? fromDate;
    String? toDate;

    if (dateRange == DateRange.customDate) {
      if (customFromDate != null && customToDate != null) {
        fromDate = DateTime(customFromDate.year, customFromDate.month, customFromDate.day).toUniversalTimeStartOfDay().toString();
        toDate = DateTime(customToDate.year, customToDate.month, customToDate.day, 23, 59, 59).toUtc().toString();
      }
    } else {
      final dates = _getDateTimeFromRange(dateRange);
      fromDate = dates.$1;
      toDate = dates.$2;
    }

    return AttendanceHistoryParams(
      pageNumber: 1,
      pageSize: 10,
      fromDate: fromDate,
      toDate: toDate,
      dateRange: dateRange,
    );
  }

  /// Helper method to get date range based on DateRange enum (similar to other filter blocs)
  (String?, String?) _getDateTimeFromRange(DateRange selectedDateRange) {
    switch (selectedDateRange) {
      case DateRange.today:
        final today = DateTime.now().toUserTimeZone()!;
        final fromDate = DateTime(today.year, today.month, today.day).toUniversalTimeStartOfDay().toString();
        final toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        return (fromDate, toDate);
      case DateRange.yesterday:
        final yesterday = DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 1));
        final fromDate = DateTime(yesterday.year, yesterday.month, yesterday.day).toUniversalTimeStartOfDay().toString();
        final toDate = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59).toUtc().toString();
        return (fromDate, toDate);
      case DateRange.lastSevenDays:
        final today = DateTime.now().toUserTimeZone()!;
        final sevenDaysAgo = today.subtract(const Duration(days: 7));
        final fromDate = DateTime(sevenDaysAgo.year, sevenDaysAgo.month, sevenDaysAgo.day).toUniversalTimeStartOfDay().toString();
        final toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        return (fromDate, toDate);
      case DateRange.lastTwentyEightDays:
        final today = DateTime.now().toUserTimeZone()!;
        final twentyEightDaysAgo = today.subtract(const Duration(days: 28));
        final fromDate = DateTime(twentyEightDaysAgo.year, twentyEightDaysAgo.month, twentyEightDaysAgo.day).toUniversalTimeStartOfDay().toString();
        final toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();
        return (fromDate, toDate);
      case DateRange.customDate:
        return (null, null);
    }
  }

  String? _getEmptyStateMessage(AttendanceHistoryParams params, bool isEmpty) {
    if (!isEmpty) return null;

    // Generate contextual message based on date range
    if (params.dateRange != null) {
      switch (params.dateRange!) {
        case DateRange.today:
          return "No logs found for today.";
        case DateRange.yesterday:
          return "No logs found for yesterday.";
        case DateRange.lastSevenDays:
          return "No logs found for the last 7 days.";
        case DateRange.lastTwentyEightDays:
          return "No logs found for the last 28 days.";
        case DateRange.customDate:
          return "No logs found for the selected date range.";
      }
    }

    return "No attendance logs found.";
  }
}
