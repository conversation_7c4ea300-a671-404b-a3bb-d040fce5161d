import 'package:fpdart/fpdart.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/core_main/utilities/type_def.dart';
import 'package:leadrat/features/attendance/data/data_source/remote/attendance_remote_data_source.dart';
import 'package:leadrat/features/attendance/data/models/attendance_logs_list_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_model.dart';
import 'package:leadrat/features/attendance/domain/entities/attendance_settings_entity.dart';
import '../../domain/repository/attendance_repository.dart';
import '../models/attendance_post_model.dart';

class AttendanceRepositoryImpl extends AttendanceRepository {
  final AttendanceRemoteDataSource _attendanceRemoteDataSource;

  AttendanceRepositoryImpl(this._attendanceRemoteDataSource);

  @override
  FutureEitherFailure<List<AttendanceLogsListModel>?> getAttendanceHistory({
    int pageNumber = 1,
    int pageSize = 10,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final result = await _attendanceRemoteDataSource.getAttendanceHistory(
        pageNumber: pageNumber,
        pageSize: pageSize,
        fromDate: fromDate,
        toDate: toDate,
      );
      return right(result);
    } catch (e) {
      return left(Failure(e.toString()));
    }
  }

  @override
  FutureEitherFailure<List<AttendanceModel>?> getAttendanceLogByUser(String userId, String timeZone) async {
    try {
      final result = await _attendanceRemoteDataSource.getAttendanceLogByUser(userId, timeZone);
      return right(result);
    } catch (e) {
      return left(Failure(e.toString()));
    }
  }

  @override
  FutureEitherFailure<AttendanceSettingsEntity?> getAttendanceSettings() async {
    try {
      final result = await _attendanceRemoteDataSource.getAttendanceSettings();
      return right(result?.toEntity() ?? AttendanceSettingsEntity());
    } catch (e) {
      return left(Failure(e.toString()));
    }
  }

  @override
  FutureEitherFailure<String?> postClockInAttendance(AttendancePostModel clockInModel) async {
    try {
      final result = await _attendanceRemoteDataSource.postClockInAttendance(clockInModel);
      return right(result ?? ' ');
    } catch (e) {
      return left(Failure(e.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> postClockOutAttendance(AttendancePostModel clockOutModel) async {
    try {
      final result = await _attendanceRemoteDataSource.postClockOutAttendance(clockOutModel);
      return right(result ?? false);
    } catch (e) {
      return left(Failure(e.toString()));
    }
  }

  @override
  FutureEitherFailure<List<AttendanceModel>?> getTodaysClockInsByWithTimeZone(String userId, String timeZoneId, String baseUtcOffset, String startTime) async {
    try {
      final result = await _attendanceRemoteDataSource.getTodaysClockInsByWithTimeZone(userId, timeZoneId, baseUtcOffset, startTime);
      return right(result);
    } catch (e) {
      return left(Failure(e.toString()));
    }
  }
}
