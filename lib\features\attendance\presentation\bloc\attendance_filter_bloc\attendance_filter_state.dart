part of 'attendance_filter_bloc.dart';

@immutable
class AttendanceFilterState {
  final DateRange selectedDateRange;
  final DateTime? selectedFromDate;
  final DateTime? selectedToDate;

  const AttendanceFilterState({
    this.selectedDateRange = DateRange.today,
    this.selectedFromDate,
    this.selectedToDate,
  });

  AttendanceFilterState copyWith({
    DateRange? selectedDateRange,
    DateTime? selectedFromDate,
    DateTime? selectedToDate,
  }) {
    return AttendanceFilterState(
      selectedDateRange: selectedDateRange ?? this.selectedDateRange,
      selectedFromDate: selectedFromDate ?? this.selectedFromDate,
      selectedToDate: selectedToDate ?? this.selectedToDate,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttendanceFilterState && other.selectedDateRange == selectedDateRange && other.selectedFromDate == selectedFromDate && other.selectedToDate == selectedToDate;
  }

  @override
  int get hashCode => selectedDateRange.hashCode ^ selectedFromDate.hashCode ^ selectedToDate.hashCode;
}
