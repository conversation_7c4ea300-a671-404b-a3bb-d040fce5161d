import 'package:leadrat/features/attendance/data/models/attendance_logs_list_model.dart';

import '../../../../core_main/utilities/type_def.dart';
import '../../data/models/attendance_model.dart';
import '../../data/models/attendance_post_model.dart';
import '../entities/attendance_settings_entity.dart';

abstract class AttendanceRepository {
  FutureEitherFailure<List<AttendanceLogsListModel>?> getAttendanceHistory({
    int pageNumber = 1,
    int pageSize = 10,
    String? fromDate,
    String? toDate,
  });

  FutureEitherFailure<List<AttendanceModel>?> getAttendanceLogByUser(String userId, String timeZone);

  FutureEitherFailure<AttendanceSettingsEntity?> getAttendanceSettings();

  FutureEitherFailure<String?> postClockInAttendance(AttendancePostModel clockInModel);

  FutureEitherFailure<bool?> postClockOutAttendance(AttendancePostModel clockOutModel);

  FutureEitherFailure<List<AttendanceModel>?> getTodaysClockInsByWithTimeZone(String userId, String timeZoneId, String baseUtcOffset, String startTime);
}
