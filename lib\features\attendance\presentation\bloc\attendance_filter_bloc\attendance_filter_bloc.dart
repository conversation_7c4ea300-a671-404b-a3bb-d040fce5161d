import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';

part 'attendance_filter_event.dart';
part 'attendance_filter_state.dart';

class AttendanceFilterBloc extends Bloc<AttendanceFilterEvent, AttendanceFilterState> {
  AttendanceFilterBloc() : super(const AttendanceFilterState()) {
    on<InitializeAttendanceFilterEvent>(_onInitializeAttendanceFilter);
    on<SelectDateRangeEvent>(_onSelectDateRange);
    on<SelectFromDateEvent>(_onSelectFromDate);
    on<SelectToDateEvent>(_onSelectToDate);
    on<ResetAttendanceFilterEvent>(_onResetFilter);
  }

  void _onInitializeAttendanceFilter(
    InitializeAttendanceFilterEvent event,
    Emitter<AttendanceFilterState> emit,
  ) {
    emit(state.copyWith(
      selectedDateRange: event.selectedDateRange,
    ));
  }

  void _onSelectDateRange(
    SelectDateRangeEvent event,
    Emitter<AttendanceFilterState> emit,
  ) {
    emit(state.copyWith(
      selectedDateRange: event.dateRange,
      // Clear custom dates when selecting a predefined range
      selectedFromDate: event.dateRange == DateRange.customDate ? state.selectedFromDate : null,
      selectedToDate: event.dateRange == DateRange.customDate ? state.selectedToDate : null,
    ));
  }

  void _onSelectFromDate(
    SelectFromDateEvent event,
    Emitter<AttendanceFilterState> emit,
  ) {
    emit(state.copyWith(
      selectedFromDate: event.selectedFromDate,
    ));
  }

  void _onSelectToDate(
    SelectToDateEvent event,
    Emitter<AttendanceFilterState> emit,
  ) {
    emit(state.copyWith(
      selectedToDate: event.selectedToDate,
    ));
  }

  void _onResetFilter(
    ResetAttendanceFilterEvent event,
    Emitter<AttendanceFilterState> emit,
  ) {
    emit(const AttendanceFilterState(
      selectedDateRange: DateRange.today,
      selectedFromDate: null,
      selectedToDate: null,
    ));
  }
}
