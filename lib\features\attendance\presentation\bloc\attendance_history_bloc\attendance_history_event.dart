part of 'attendance_history_bloc.dart';

@immutable
sealed class AttendanceHistoryEvent {}

final class GetAllAttendanceHistoryEvent extends AttendanceHistoryEvent {
  final AttendanceHistoryParams? params;

  GetAllAttendanceHistoryEvent({this.params});
}

final class LoadMoreAttendanceHistoryEvent extends AttendanceHistoryEvent {}

final class ApplyAttendanceFilterEvent extends AttendanceHistoryEvent {
  final AttendanceHistoryParams params;

  ApplyAttendanceFilterEvent({required this.params});
}
