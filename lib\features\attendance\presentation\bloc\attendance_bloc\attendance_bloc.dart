import 'dart:async';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_custom_camera.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/attendance/data/models/attendance_logs_list_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_post_model.dart';
import 'package:leadrat/features/attendance/domain/entities/attendance_settings_entity.dart';
import 'package:leadrat/features/attendance/domain/usecase/attendance_history_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/get_attendance_logs_by_user_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/get_attendance_settings_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/post_clockin_attendance_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/post_clockout_attendance_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/attendance_history_params.dart';
import 'package:leadrat/features/attendance/presentation/items/clock_in_out_item.dart';
import 'package:leadrat/main.dart';

import '../../../../../core_main/common/data/user/models/user_details_clockin_model.dart';
import '../../../../../core_main/enums/app_enum/blob_folder_names.dart';
import '../../../../../core_main/services/native_implementation_service/native_implementation_service.dart';
import '../../../../../core_main/utilities/utility.dart';
import '../../../../user_profile/domain/usecase/upload_document_usecase.dart';

part 'attendance_event.dart';

part 'attendance_state.dart';

class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final GetAttendanceLogsByUserUseCase _getAttendanceLogsByUserUseCase;
  final GetAttendanceLogsByUserTimeZoneUseCase _getAttendanceLogsByUserTimeZoneUseCase;
  final GetAttendanceSettingsUseCase _getAttendanceSettingsUseCase;
  final PostClockInAttendanceUseCase _postClockInAttendanceUseCase;
  final PostClockOutAttendanceUseCase _postClockOutAttendanceUseCase;
  final UploadDocumentUseCase _uploadDocumentUseCase;
  final NativeImplementationService _nativeImplementationService;
  final AttendanceHistoryUseCase _attendanceHistoryUseCase;

  static AttendanceSettingsEntity? attendanceSettingsEntity;
  static List<AttendanceModel>? todayAttendanceLogs;
  UserDetailsClockInModel? _geoFenceModel;

  AttendanceBloc(
    this._getAttendanceLogsByUserUseCase,
    this._getAttendanceSettingsUseCase,
    this._postClockInAttendanceUseCase,
    this._postClockOutAttendanceUseCase,
    this._uploadDocumentUseCase,
    this._nativeImplementationService,
    this._getAttendanceLogsByUserTimeZoneUseCase,
    this._attendanceHistoryUseCase,
  ) : super(const AttendanceState()) {
    on<ToggleClockInEvent>(_onToggleClockIn);
    on<GetAttendanceSettingsEvent>(_getAttendanceSettings);
    on<GetTodayAttendanceLogEvent>(_getTodaysAttendanceLog);
    on<PunchLocationEvent>(_onPunchLocation);
    on<RefreshWorkingTimeEvent>(_onRefreshWorkingTime);
    on<GetAllAttendanceHistoryEvent>(_getAttendanceHistory);
    on<GetGreetingAndAnimationEvent>(_getGreetingAndAnimation);
  }

  FutureOr<void> _getAttendanceSettings(GetAttendanceSettingsEvent event, Emitter<AttendanceState> emit) async {
    final isUserGeoFenceEnabled = getIt<UsersDataRepository>().getLoggedInUser()?.isGeoFenceActive ?? false;
    var result = await _getAttendanceSettingsUseCase.call(NoParams());
    result.fold(
        (failure) => {},
        (res) => {
              attendanceSettingsEntity = res,
            });
    final isGeoFenceEnabled = (attendanceSettingsEntity?.isGeoFenceEnabled ?? false) && isUserGeoFenceEnabled;
    emit(state.copyWith(isGeoFenceEnabled: isGeoFenceEnabled));
    if (isGeoFenceEnabled) {
      var response = await getIt<UsersDataRepository>().getGeoFenceDetails();
      if (response != null) {
        _geoFenceModel = response;
      }
    }
  }

  FutureOr<void> _getTodaysAttendanceLog(GetTodayAttendanceLogEvent event, Emitter<AttendanceState> emit) async {
    emit(state.copyWith(clockedInState: AttendanceLogInState.loading));
    await storeTheAttendanceLogsLocally();
    final clockInOutItem = ClockInOutItem(
      clockInTimeStamp: todayAttendanceLogs?.firstOrNull?.clockInTime != null && todayAttendanceLogs?.firstOrNull?.clockOutTime == null ? DateFormat('hh:mm a').format(todayAttendanceLogs?.firstOrNull?.clockInTime ?? DateTime(DateTime.now().year)) : "--",
      clockOutTimeStamp: "--",
      isClockedIn: todayAttendanceLogs!.isNotEmpty ? todayAttendanceLogs?.firstOrNull?.clockOutTime == null : false,
    );

    List<ClockInOutItem> attendanceLogs = [];
    todayAttendanceLogs?.forEach((attendanceLog) {
      attendanceLogs.add(ClockInOutItem(
        clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
        clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
        clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
        clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
        logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
        isClockedIn: attendanceLog.clockOutTime != null ? false : true,
      ));
    });
    String? currentWorkingTime;
    final isCurrentlyClockedIn = clockInOutItem.isClockedIn ?? false;
    if (isCurrentlyClockedIn && todayAttendanceLogs?.firstOrNull?.clockInTime != null) {
      var clockInTime = todayAttendanceLogs!.firstOrNull!.clockInTime!;
      var currentTime = DateTime.now().toUserTimeZone() ?? DateTime.now();
      var duration = currentTime.difference(clockInTime);

      var hours = duration.inHours;
      var minutes = duration.inMinutes % 60;
      var seconds = duration.inSeconds % 60;

      currentWorkingTime = "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
    }

    emit(state.copyWith(
      clockedInState: AttendanceLogInState.success,
      clockInOutItem: clockInOutItem,
      attendanceList: attendanceLogs,
      currentWorkingTime: currentWorkingTime,
    ));
  }

  FutureOr<void> _getAttendanceHistory(GetAllAttendanceHistoryEvent event, Emitter<AttendanceState> emit) async {
    // Create default params for today's attendance history
    final today = DateTime.now().toUserTimeZone();
    final fromDate = DateTime(today!.year, today.month, today.day).toUniversalTimeStartOfDay().toString();
    final toDate = DateTime(today.year, today.month, today.day, 23, 59, 59).toUtc().toString();

    final params = AttendanceHistoryParams(
      pageNumber: 1,
      pageSize: 10,
      fromDate: fromDate,
      toDate: toDate,
    );

    var result = await _attendanceHistoryUseCase.call(params);
    result.fold(
        (failure) => {},
        (res) => {
              emit(state.copyWith(clockedInState: AttendanceLogInState.success, attendanceHistoryList: res)),
            });
  }

  FutureOr<void> _onToggleClockIn(ToggleClockInEvent event, Emitter<AttendanceState> emit) async {
    try {
      final isUserGeoFenceEnabled = getIt<UsersDataRepository>().getLoggedInUser()?.isGeoFenceActive ?? false;
      if (attendanceSettingsEntity == null) {
        var attendanceSettingsResult = await _getAttendanceSettingsUseCase.call(NoParams());
        attendanceSettingsResult.fold(
            (failure) => {
                  emit(state.copyWith(clockedInState: AttendanceLogInState.errorInLoadingAttendance)),
                },
            (res) => {
                  attendanceSettingsEntity = res,
                });

        if ((attendanceSettingsEntity?.isGeoFenceEnabled ?? false) && isUserGeoFenceEnabled) {
          var response = await getIt<UsersDataRepository>().getGeoFenceDetails();
          if (response != null) {
            _geoFenceModel = response;
          }
        }
      }
      if (isUserGeoFenceEnabled && (attendanceSettingsEntity?.isGeoFenceEnabled ?? false) && _geoFenceModel?.id != "00000000-0000-0000-0000-000000000000") {
        try {
          bool isInsideFence = await isUserWithinAnyFence(_geoFenceModel ?? UserDetailsClockInModel());
          if (!isInsideFence) {
            emit(state.copyWith(clockedInState: AttendanceLogInState.notInProximity));
            LeadratCustomSnackbar.show(
              message: "${state.clockInOutItem?.isClockedIn ?? false ? 'Clock-out' : 'Clock-in'} failed: You are outside the permitted set geo-fence area. Please move within the allowed boundary to proceed.",
              type: SnackbarType.warning,
              navigatorKey: MyApp.navigatorKey,
            );
            return;
          }
        } on PermissionDeniedException catch (e) {
          LeadratCustomSnackbar.show(
            message: 'Location permission is mandatory to use the "Clock In / Out" feature.',
            type: SnackbarType.error,
            navigatorKey: MyApp.navigatorKey,
          );
          DialogManager().hideTransparentProgressDialog();
          return;
        } catch (ex) {
          LeadratCustomSnackbar.show(message: "Some thing went wrong. Please try again.", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
          DialogManager().hideTransparentProgressDialog();
          return;
        }
      }

      if (event.isClockedIn ?? false) {
        emit(state.copyWith(clockedInState: AttendanceLogInState.clockingOut));
        var location = await _nativeImplementationService.getCurrentLocation();
        if (location != null) {
          var currentPlaceMark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location.latitude, location.longitude);
          if (attendanceSettingsEntity != null && (attendanceSettingsEntity?.isSelfieMandatoryForClockOut ?? false)) {
            DialogManager().hideTransparentProgressDialog();
            var capturedImage = Platform.isAndroid ? await LeadratCustomCamera.open(lensDirection: CameraLensDirection.front) : await _nativeImplementationService.openCamera();
            if (capturedImage != null) {
              emit(state.copyWith(clockedInState: AttendanceLogInState.clockingOut));
              var params = AttendancePostModel(
                latitude: location.latitude,
                longitude: location.longitude,
                locationName: "${currentPlaceMark?.subLocality ?? ""}, ${currentPlaceMark?.locality ?? ""}, ${currentPlaceMark?.administrativeArea ?? ""}",
                clockInImageUrl: null,
                clockOutImageUrl: await uploadImageToS3Bucket(capturedImage),
              );
              var result = await _postClockOutAttendanceUseCase.call(params);
              result.fold(
                  (failure) => {
                        emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingOut)),
                      },
                  (res) => {});
              if (result.isRight()) {
                final ClockInOutItem clockInOutItem = ClockInOutItem(
                  clockOutTimeStamp: "--",
                  clockInTimeStamp: "--",
                  isClockedIn: false,
                );

                List<ClockInOutItem>? attendanceLogs = [];
                add(GetAllAttendanceHistoryEvent());
                await storeTheAttendanceLogsLocally();
                todayAttendanceLogs?.forEach((attendanceLog) {
                  attendanceLogs.add(ClockInOutItem(
                    clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                    clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                    clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                    clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                    logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                    isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                  ));
                });
                emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedOut, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
              }
            } else {
              emit(state.copyWith(clockedInState: AttendanceLogInState.selfieIsMandatory));
            }
          } else {
            var params = AttendancePostModel(
              latitude: location.latitude,
              longitude: location.longitude,
              locationName: "${currentPlaceMark?.subLocality ?? ""}, ${currentPlaceMark?.locality ?? ""}, ${currentPlaceMark?.administrativeArea ?? ""}",
              clockInImageUrl: null,
              clockOutImageUrl: null,
            );
            var result = await _postClockOutAttendanceUseCase.call(params);
            result.fold(
                (failure) => {
                      emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingOut)),
                    },
                (res) => {});
            if (result.isRight()) {
              final ClockInOutItem clockInOutItem = ClockInOutItem(
                clockOutTimeStamp: "--",
                clockInTimeStamp: "--",
                isClockedIn: false,
              );

              List<ClockInOutItem>? attendanceLogs = [];
              await storeTheAttendanceLogsLocally();
              todayAttendanceLogs?.forEach((attendanceLog) {
                attendanceLogs.add(ClockInOutItem(
                  clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                  clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                  clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                  clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                  logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                  isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                ));
              });
              emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedOut, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
            }
          }
        } else {
          emit(state.copyWith(clockedInState: AttendanceLogInState.locationMandatory));
        }
      } else {
        emit(state.copyWith(clockedInState: AttendanceLogInState.clockingIn));
        var location = await _nativeImplementationService.getCurrentLocation();
        if (location != null) {
          var currentPlacemark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location.latitude, location.longitude);
          if (attendanceSettingsEntity != null && (attendanceSettingsEntity?.isSelfieMandatoryForClockIn ?? false)) {
            DialogManager().hideTransparentProgressDialog();
            var capturedImage = Platform.isAndroid ? await LeadratCustomCamera.open(lensDirection: CameraLensDirection.front) : await _nativeImplementationService.openCamera();
            if (capturedImage != null) {
              emit(state.copyWith(clockedInState: AttendanceLogInState.clockingIn));
              var params = AttendancePostModel(
                latitude: location.latitude,
                longitude: location.longitude,
                locationName: [currentPlacemark?.subLocality, currentPlacemark?.locality, currentPlacemark?.administrativeArea].where((e) => e != null && e.isNotEmpty).join(", "),
                clockInImageUrl: await uploadImageToS3Bucket(capturedImage),
                clockOutImageUrl: null,
              );
              var result = await _postClockInAttendanceUseCase.call(params);
              result.fold(
                  (failure) => {
                        emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingIn)),
                      },
                  (res) => {});
              if (result.isRight()) {
                final ClockInOutItem clockInOutItem = ClockInOutItem(
                  clockInTimeStamp: DateFormat('hh:mm a').format(DateTime.now().toUserTimeZone()!),
                  clockOutTimeStamp: "--",
                  isClockedIn: true,
                );

                List<ClockInOutItem>? attendanceLogs = [];

                todayAttendanceLogs?.insert(
                    0,
                    AttendanceModel(
                      clockInTime: DateTime.now().toUserTimeZone()!,
                      clockOutTime: null,
                      clockInLocation: params.locationName ?? "",
                      clockOutLocation: null,
                    ));

                todayAttendanceLogs?.forEach((attendanceLog) {
                  attendanceLogs.add(ClockInOutItem(
                    clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                    clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                    clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                    clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                    logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                    isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                  ));
                });
                emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedIn, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
              }
            } else {
              emit(state.copyWith(clockedInState: AttendanceLogInState.selfieIsMandatory));
            }
          } else {
            var params = AttendancePostModel(
              latitude: location.latitude,
              longitude: location.longitude,
              locationName: [currentPlacemark?.subLocality, currentPlacemark?.locality, currentPlacemark?.administrativeArea].where((e) => e != null && e.isNotEmpty).join(", "),
              clockInImageUrl: null,
              clockOutImageUrl: null,
            );
            var result = await _postClockInAttendanceUseCase.call(params);
            result.fold(
                (failure) => {
                      emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingIn)),
                    },
                (res) => {});
            if (result.isRight()) {
              final ClockInOutItem clockInOutItem = ClockInOutItem(
                clockInTimeStamp: DateFormat('hh:mm a').format(DateTime.now().toUserTimeZone()!),
                clockOutTimeStamp: "--",
                isClockedIn: true,
              );

              List<ClockInOutItem>? attendanceLogs = [];

              todayAttendanceLogs?.insert(
                  0,
                  AttendanceModel(
                    clockInTime: DateTime.now().toUserTimeZone(),
                    clockOutTime: null,
                    clockInLocation: params.locationName ?? "",
                    clockOutLocation: null,
                  ));

              todayAttendanceLogs?.forEach((attendanceLog) {
                attendanceLogs.add(ClockInOutItem(
                  clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                  clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                  clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                  clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                  logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                  isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                ));
              });
              emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedIn, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
            }
          }
        } else {
          emit(state.copyWith(clockedInState: AttendanceLogInState.locationMandatory));
        }
      }
    } on TimeoutException catch (e) {
      LeadratCustomSnackbar.show(message: e.message ?? "Request timed out. Please try again.", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Some thing went wrong. Please try again.", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
    }
  }

  Future<bool> isUserWithinAnyFence(UserDetailsClockInModel model) async {
    Position currentPosition = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    final double userLat = currentPosition.latitude;
    final double userLon = currentPosition.longitude;

    double radius = (model.geoFenceRadius ?? 50).toDouble();
    if (model.radiusUnit == RadiusUnit.kiloMeter) {
      radius *= 1000;
    }

    for (var project in model.projects ?? []) {
      if (project.latitude != null && project.longitude != null) {
        bool isNearby = Utility.isWithinRadius(
          sourceLat: userLat,
          sourceLon: userLon,
          targetLat: project.latitude!,
          targetLon: project.longitude!,
          radiusInMeters: radius,
        );
        if (isNearby) return true;
      }
    }

    for (var property in model.properties ?? []) {
      if (property.latitude != null && property.longitude != null) {
        bool isNearby = Utility.isWithinRadius(
          sourceLat: userLat,
          sourceLon: userLon,
          targetLat: property.latitude!,
          targetLon: property.longitude!,
          radiusInMeters: radius,
        );
        if (isNearby) return true;
      }
    }

    return false;
  }

  Future<String?> uploadImageToS3Bucket(XFile? capturedImage) async {
    if (capturedImage != null) {
      String? uploadedImage;
      var convertedFile = File(capturedImage.path);
      final uploadedImageToS3Bucket = await _uploadDocumentUseCase(UploadDocumentParams(BlobFolderNameEnum.attendanceImages.description, await _nativeImplementationService.getImageExtension(capturedImage) ?? 'jpg', convertedFile));
      uploadedImageToS3Bucket.fold(
          (failure) => {
                // show toast
              },
          (res) => {
                uploadedImage = res?.isNotEmpty ?? false ? res : null,
              });
      return uploadedImage;
    } else {
      return null;
    }
  }

  Future<void> storeTheAttendanceLogsLocally() async {
    final userDetails = getIt<UsersDataRepository>().getLoggedInUser();
    final userTimeZoneInfo = userDetails?.timeZoneInfo;
    if (userTimeZoneInfo?.baseUTcOffset != null && userTimeZoneInfo?.timeZoneDisplay != null && userTimeZoneInfo?.timeZoneId != null && userTimeZoneInfo?.timeZoneName != null) {
      final usecaseParams = GetAllAttendanceLogsUsecaseParams(userId: userDetails?.userId ?? "", timeZone: userTimeZoneInfo?.timeZoneId, baseUtcOffset: userTimeZoneInfo?.baseUTcOffset, startTime: DateTime.now().toUniversalTimeStartOfDay().toString());
      var latestAttendanceLog = await _getAttendanceLogsByUserTimeZoneUseCase.call(usecaseParams);
      latestAttendanceLog.fold(
          (failure) => {
                // Show toast for the error message
              }, (res) {
        todayAttendanceLogs = res;
      });
    } else {
      final timeZoneId = DateTime.now().timeZoneName;
      final usecaseParams = GetAllAttendanceLogsUsecaseParams(userId: userDetails?.userId ?? "", timeZone: timeZoneId);
      var latestAttendanceLog = await _getAttendanceLogsByUserUseCase.call(usecaseParams);
      latestAttendanceLog.fold(
          (failure) => {
                // Show toast for the error message
              }, (res) {
        todayAttendanceLogs = res;
      });
    }
  }

  FutureOr<void> _onPunchLocation(PunchLocationEvent event, Emitter<AttendanceState> emit) async {
    try {
      emit(state.copyWith(clockedInState: AttendanceLogInState.punchingLocation, isPunchingLocation: true));
      var location = await _nativeImplementationService.getCurrentLocation();
      if (location != null) {
        var currentPlaceMark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location.latitude, location.longitude);
        var params = AttendancePostModel(
          latitude: location.latitude,
          longitude: location.longitude,
          locationName: "${currentPlaceMark?.subLocality ?? ""}, ${currentPlaceMark?.locality ?? ""}, ${currentPlaceMark?.administrativeArea ?? ""}",
          clockInImageUrl: null,
          clockOutImageUrl: null,
          isPunchInLocation: true,
          message: event.comment,
        );
        var result = await _postClockInAttendanceUseCase.call(params);
        result.fold(
            (failure) => {
                  emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingOut)),
                },
            (res) => {
                  add(GetAllAttendanceHistoryEvent()),
                  emit(state.copyWith(clockedInState: AttendanceLogInState.punchLocationSuccess, isPunchingLocation: false)),
                });
      }
    } catch (e, stackTrace) {
      emit(state.copyWith(
        clockedInState: AttendanceLogInState.punchLocationError,
        isPunchingLocation: false,
      ));
      e.logException(stackTrace);
    }
  }

  FutureOr<void> _onRefreshWorkingTime(RefreshWorkingTimeEvent event, Emitter<AttendanceState> emit) async {
    if (todayAttendanceLogs?.isNotEmpty == true && todayAttendanceLogs?.firstOrNull?.clockOutTime == null) {
      var clockInTime = todayAttendanceLogs?.firstOrNull?.clockInTime;
      if (clockInTime != null) {
        var currentTime = DateTime.now().toUserTimeZone() ?? DateTime.now();
        var duration = currentTime.difference(clockInTime);

        var hours = duration.inHours;
        var minutes = duration.inMinutes % 60;
        var seconds = duration.inSeconds % 60;

        var workingTime = "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";

        emit(state.copyWith(currentWorkingTime: workingTime));
      }
    }
  }

  Future<void> _getGreetingAndAnimation(GetGreetingAndAnimationEvent event, Emitter<AttendanceState> emit) async {
    final now = DateTime.now();
    const timeGreetings = {
      6: {'greeting': 'Good Morning', 'animation': LottieResources.goodMorningAnimation},
      12: {'greeting': 'Good Afternoon', 'animation': LottieResources.goodAfterNoonAnimation},
      16: {'greeting': 'Good Evening', 'animation': LottieResources.goodNightAnimation},
    };

    final entry = timeGreetings.entries.lastWhere(
      (entry) => now.hour >= entry.key,
      orElse: () => timeGreetings.entries.first,
    );

    emit(state.copyWith(
      partOfTheDayAnimation: entry.value['animation'],
    ));
  }
}
