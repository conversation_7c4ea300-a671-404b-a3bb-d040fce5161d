import 'package:leadrat/core_main/utilities/type_def.dart';
import 'package:leadrat/features/attendance/domain/repository/attendance_repository.dart';
import 'package:leadrat/features/attendance/domain/usecase/attendance_history_params.dart';

import '../../../../core_main/common/base/usecase/use_case.dart';
import '../../data/models/attendance_logs_list_model.dart';

class AttendanceHistoryUseCase implements UseCase<List<AttendanceLogsListModel>?, AttendanceHistoryParams> {
  final AttendanceRepository _attendanceRepository;

  AttendanceHistoryUseCase(this._attendanceRepository);

  @override
  FutureEitherFailure<List<AttendanceLogsListModel>?> call(AttendanceHistoryParams params) async {
    return await _attendanceRepository.getAttendanceHistory(
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
      fromDate: params.fromDate,
      toDate: params.toDate,
    );
  }
}
